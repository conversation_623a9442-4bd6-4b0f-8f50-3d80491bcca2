<?php

namespace App\Repositories\ArticleThumbnail;

use App\V4\Models\ArticleThumbnail;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class ArticleThumbnailRepository
{

    public function getArticleThumbnailByArticleUniqueId($articleUniqueId)
    {
        return ArticleThumbnail::where('articleUniqueId', $articleUniqueId)->first();
    }

    public function upsertArticleThumbnail(string $articleUniqueId, string $originalMediaUrl, ?string $caption = ''): ArticleThumbnail
    {
        return ArticleThumbnail::updateOrCreate([
            'articleUniqueId' => $articleUniqueId,
        ],
        [
            'articleUniqueId' => $articleUniqueId,
            'originalMediaUrl' => $originalMediaUrl,
            'squareUrl' => imageProxy(nw_bunker('imageproxy', 'key', ''), $originalMediaUrl, "1000x1000", "q50"),
            'wideUrl' => imageProxy(nw_bunker('imageproxy', 'key', ''), $originalMediaUrl, "1000x400", "q50"),
            'caption' => $caption,
            'squareImageSize' => 1000,
            'wideImageWidth' => 1000,
            'wideImageHeight' => 400,
            'updatedAt' => Carbon::now(),
            'createdAt' => Carbon::now(),
        ]);
    }

}