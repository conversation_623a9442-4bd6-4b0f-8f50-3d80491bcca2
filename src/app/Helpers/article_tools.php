<?php

use App\V4\Services\TopicService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

define("NW_ARTICLE", "a");
define("NW_PODCAST", "p");
define("NW_VIDEO", "v");
define("NW_MIKROWAV", "m");



function isArticle($id): bool
{
    return like_match('A%_%', $id) || like_match('covid19__%', $id) || like_match('sa%__%', $id)  || (!Str::contains($id, '_') && strlen($id) <= 13 &&  strlen($id) >= 10);
}

function isVideo($id): bool
{
    return like_match('V%_%', $id);
}

function isPodCast($id): bool
{
    return like_match('P%_%', $id);
}

function isPoll($id): bool
{
    return like_match("A%_P_%", $id);
}

function isAd($id): bool
{
    return like_match("AD_%", $id);
}

function isMikrowav($id): bool
{
    return (bool) preg_match("/^M\d{12,}/i", $id);
}

function getEntityType($id): String
{
    if (isArticle($id)) return NW_ARTICLE;
    if (isVideo($id)) return NW_VIDEO;
    if (isPodCast($id)) return NW_PODCAST;
    if (isMikrowav($id)) return NW_MIKROWAV;
    throw new \Exception("Unkown entity type " . $id);
}

function generateUniqueId($type = "A"){
    $date = Carbon::now()->format('ym');
    $prefix = "{$type}{$date}_";
    return $prefix.Str::random(6);
}

function slugify($title) {

    $string = preg_replace('/[^\\pL\d_]+/u', '-', $title);
    // convert all to lowercase
    $string = trim(mb_strtolower($string), '-');
    // clean up multiple dashes or whitespaces
    $string = preg_replace("/[\s-]+/", " ", $string);
    // convert whitespaces and underscore to dash
    $string = preg_replace("/[\s_]/", "-", $string);
    // remove unwanted character
    $string = preg_replace("/[^a-z0-9\.\-\P{Han}]+/i", "", $string);
    // limit character
    $string = trim(mb_substr($string, 0, 75), '-');

    return $string;
}

function getUniqueIdFromPermalink($permalink) {
    $explodeId = explode('-', rtrim($permalink, "-"));
    return trimSpecialCharacter(end($explodeId));
}

function trimSpecialCharacter($string) {
    //remove any special character (encoded eg: %0A aka new line)
    if (strpos($string, '%') !== false){
        $string = urldecode($string);
    }
    $string = preg_replace('/[^a-zA-Z0-9_-]/s','',$string);
    return $string;
}

function getSlugUrl($content) {
    $type = $content->contentType;
    return !empty($content->meta->$type->permalink) ? $content->meta->$type->permalink : (!empty($content->uniqueId) ? $content->uniqueId : $content->unique_id);
}

function replaceMediaInOldArticle($article) {

    // NWGEN-1540: some channels have publisher id 0, hence publisher turns up null
    if (property_exists($article, 'publisher') && $article->publisher === null) {
        abort(404, 'Article not found');
    }

    if (property_exists($article,'publisher') && $article->publisher->ranking === 'newswav') {
        return $article;
    }

    // Requested to exclude: https://newswav.atlassian.net/browse/NWGEN-939?focusedCommentId=17998
    $publisherIdsToExclude = nw_bunker('publisher', 'exclude_expire_media', []);
    foreach($publisherIdsToExclude as $publisherIdToExclude) {
        if ($article->publisher->id == $publisherIdToExclude) {
            return $article;
        }
    }
    
    // Get article unique ID - handle both uniqueID and unique_id property names
    $articleUniqueId = $article->uniqueID ?? $article->unique_id ?? null;
    
    if (!$articleUniqueId) {
        return $article;
    }
    
    // Get article thumbnail from article_thumbnail table
    $articleThumbnail = DB::table('article_thumbnail')
        ->where('articleUniqueId', $articleUniqueId)
        ->first();
    
    if (!$articleThumbnail) {
        // No thumbnail found, return article as-is
        return $article;
    }

    // Check if thumbnail has expiry info in media_expiry table
    $thumbnailExpiryInfo = DB::table('media_expiry')
        ->where('thumbnailId', $articleThumbnail->id)
        ->get();
    
    if (count($thumbnailExpiryInfo) > 0) {
        return replaceMediaInOldArticleFromExpiryMeta($article, $thumbnailExpiryInfo);
    }

    $datePast60Days = Carbon::now()->subDays(60)->format('Y-m-d H:i:s');

    if ($article->publishedDate > $datePast60Days) {
        // article is still fresh, dont expire the media contents
        Log::info('Article thumbnail not replaced: ' . $article->uniqueID . ' published ' . $article->publishedDate);
        return $article;
    }
    
    // start replacing media
    $topicId = $article->topic ?? null;
    $placeholderMainUrl = TopicService::getImageForTopic($topicId);
    $placeholderAsImgTag = "<img src=\"$placeholderMainUrl\" style=\"width:100%\" class=\"fullWidthImg\" alt=\"image is not available\">";
    $placeholderUrl = $placeholderMainUrl . '?thumbnailId=' . $articleThumbnail->id;
    
    if (isset($article->mediaArray) && is_array($article->mediaArray)) {
        foreach($article->mediaArray as $media) {
            if ($media['url']) {
                $article->html = str_replace($media['url'], $placeholderUrl, $article->html);
                $media['url'] = $placeholderUrl;
            }
            if (isset($media['squareUrl']) && $media['squareUrl']) {
                $article->html = str_replace($media['squareUrl'], $placeholderUrl, $article->html);
                $media['squareUrl'] = $placeholderUrl;
            }
            if (isset($media['wideUrl']) && $media['wideUrl']) {
                $article->html = str_replace($media['wideUrl'], $placeholderUrl, $article->html);
                $media['wideUrl'] = $placeholderUrl;
            }
            if (isset($media['thumbnail']) && $media['thumbnail']) {
                $article->html = str_replace($media['thumbnail'], $placeholderUrl, $article->html);
                $media['thumbnail'] = $placeholderUrl;
            }
        }
    }
    
    // remove all sourcesets
    $srcsetRegex = '/ srcset="[^"]+"/m';
    $article->html = preg_replace($srcsetRegex, '', $article->html, 50);

    // Replace images in <figure> elements where <figcaption> contains a hyperlink
    // This handles cases like: <figure><img src="..."><figcaption>Photo by X: <a href="...">...</a></figcaption></figure>
    $figureWithLinkedCaptionRegex = '/<figure[^>]*>.*?<img[^>]+src=["\'][^"\']+["\'][^>]*>.*?<figcaption[^>]*>.*?<a[^>]+href=["\'][^"\']+["\'][^>]*>.*?<\/a>.*?<\/figcaption>.*?<\/figure>/mis';
    $article->html = preg_replace($figureWithLinkedCaptionRegex, $placeholderAsImgTag, $article->html, 50);

    // last effort to totally ensure all <img src= are replaced
    $imgTagRegex = '/<img[^>]+src=\"(.*?)\"[^>]*>/m';
    $article->html = preg_replace_callback($imgTagRegex, function($matches) use ($placeholderMainUrl, $placeholderAsImgTag) {
        $fullMatch = $matches[0];
        if (Str::contains($fullMatch, $placeholderMainUrl)) {
            return $fullMatch;
        }
        return $placeholderAsImgTag;
    }, $article->html, 50);

    $imgTagRegexSingleQuote = '/<img[^>]+src=\'(.*?)\'[^>]*>/m';
    $article->html = preg_replace_callback($imgTagRegexSingleQuote, function($matches) use ($placeholderMainUrl, $placeholderAsImgTag) {
        $fullMatch = $matches[0];
        if (Str::contains($fullMatch, $placeholderMainUrl)) {
            return $fullMatch;
        }
        return $placeholderAsImgTag;
    }, $article->html, 50);

    return $article;
}

/**
 * @param $article
 * @param \Illuminate\Support\Collection $mediaExpiry
 * @return mixed
 */
function replaceMediaInOldArticleFromExpiryMeta($article, $mediaExpiry) {
    $now = Carbon::now()->format('Y-m-d H:i:s');
    $shouldExpireAt = Carbon::parse($article->publishedDate)->addDays(60);
    $topicId = $article->topic ?? null;
    $placeholderMainUrl = TopicService::getImageForTopic($topicId);

    // Get article unique ID - handle both uniqueID and unique_id property names
    $articleUniqueId = $article->uniqueID ?? $article->unique_id ?? null;
    
    if (!$articleUniqueId) {
        return $article;
    }

    // Get article thumbnail from article_thumbnail table
    $articleThumbnail = DB::table('article_thumbnail')
        ->where('articleUniqueId', $articleUniqueId)
        ->first();
    
    if (!$articleThumbnail) {
        // No thumbnail found, return article as-is
        return $article;
    }

    // Check media_expiry table using thumbnailId
    $thumbnailExpiryInfo = $mediaExpiry->where('thumbnailId', $articleThumbnail->id)->first();
    
    // Determine expiry date
    $expiryDate = null;
    if (isset($thumbnailExpiryInfo)) {
        $expiryDate = $thumbnailExpiryInfo->expireAt;
    } else {
        $expiryDate = $shouldExpireAt;
    }

    // If expiry date is null or not yet expired, return article as-is
    if ($expiryDate == null || $now < $expiryDate) {
        return $article;
    }

    // Media has expired, replace with placeholder
    $placeholderUrl = $placeholderMainUrl . '?thumbnailId=' . $articleThumbnail->id;
    
    // Replace thumbnail URLs in article HTML and mediaArray
    if (isset($article->mediaArray) && is_array($article->mediaArray)) {
        foreach($article->mediaArray as $media) {
            if ($media['url']) {
                $article->html = str_replace($media['url'], $placeholderUrl, $article->html);
                $media['url'] = $placeholderUrl;
            }
            if (isset($media['squareUrl']) && $media['squareUrl']) {
                $article->html = str_replace($media['squareUrl'], $placeholderUrl, $article->html);
                $media['squareUrl'] = $placeholderUrl;
            }
            if (isset($media['wideUrl']) && $media['wideUrl']) {
                $article->html = str_replace($media['wideUrl'], $placeholderUrl, $article->html);
                $media['wideUrl'] = $placeholderUrl;
            }
            if (isset($media['thumbnail']) && $media['thumbnail']) {
                $article->html = str_replace($media['thumbnail'], $placeholderUrl, $article->html);
                $media['thumbnail'] = $placeholderUrl;
            }
        }
    }

    // remove all sourcesets
    $srcsetRegex = '/ srcset="[^"]+"/m';
    $article->html = preg_replace($srcsetRegex, '', $article->html, 50);

    $placeholderAsImgTag = "<img src=\"$placeholderMainUrl\" style=\"width:100%\" class=\"fullWidthImg\" alt=\"image is not available\">";

    // Replace images in <figure> elements where <figcaption> contains a hyperlink
    // This handles cases like: <figure><img src="..."><figcaption>Photo by X: <a href="...">...</a></figcaption></figure>
    $figureWithLinkedCaptionRegex = '/<figure[^>]*>.*?<img[^>]+src=["\'][^"\']+["\'][^>]*>.*?<figcaption[^>]*>.*?<a[^>]+href=["\'][^"\']+["\'][^>]*>.*?<\/a>.*?<\/figcaption>.*?<\/figure>/mis';
    $article->html = preg_replace($figureWithLinkedCaptionRegex, $placeholderAsImgTag, $article->html, 50);

    // last effort to totally ensure all <img src= are replaced
    $imgTagRegex = '/<img[^>]+src=\"(.*?)\"[^>]*>/m';
    $article->html = preg_replace_callback($imgTagRegex, function($matches) use ($placeholderMainUrl, $placeholderAsImgTag) {
        $fullMatch = $matches[0];
        if (Str::contains($fullMatch, $placeholderMainUrl)) {
            return $fullMatch;
        }
        return $placeholderAsImgTag;
    }, $article->html, 50);

    $imgTagRegexSingleQuote = '/<img[^>]+src=\'(.*?)\'[^>]*>/m';
    $article->html = preg_replace_callback($imgTagRegexSingleQuote, function($matches) use ($placeholderMainUrl, $placeholderAsImgTag) {
        $fullMatch = $matches[0];
        if (Str::contains($fullMatch, $placeholderMainUrl)) {
            return $fullMatch;
        }
        return $placeholderAsImgTag;
    }, $article->html, 50);

    return $article;
}

/*
 * It counts the number of words in a string
 * courtesy of gpt-4,
 * WILL ONLY WORK FOR ENGLISH, MALAY & CHINESE
 * online test: https://onlinephp.io/c/48db3
 * @param string $text text string
 *
 * @return int total number of words in the text.
 */
function countWords(string $text){
    // remove all chinese characters from english/malay text
    $engText = preg_replace('/[\x{4E00}-\x{9FFF}\x{3000}-\x{303F}\x{FF00}-\x{FFEF}]/u', '', $text);
    //Count English words
    $engCount = str_word_count($engText);

    // Count Chinese characters
    preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $text, $matches);
    $chineseCount = count($matches[0]);

    // Total words count
    $totalCount = $engCount + $chineseCount;
    if (env('LOCAL_DEBUG')){
        $hash = md5($engText);
        $data = (object)[
            'text' => $text,
            'engText' => $engText,
            'engCount' => $engCount,
            'chineseCount' => $chineseCount,
            'totalCount' => $totalCount
        ];
        Storage::disk('local')->put("WordCount/debug/$hash.json", json_encode($data, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES|JSON_THROW_ON_ERROR|JSON_INVALID_UTF8_IGNORE|JSON_INVALID_UTF8_SUBSTITUTE ));
    }

    return $totalCount;
}
