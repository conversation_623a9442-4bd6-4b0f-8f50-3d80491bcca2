<?php

namespace App\V4\Repositories\Content;

use App\Repositories\Article\ArticleRepository;
use App\Repositories\Post\PostRepository;
use App\Repositories\Publisher\PublisherRepository;
use App\Repositories\Report\ReportRepository;
use App\Repositories\Topic\TopicRepository;
use App\Services\Newswav\Pin\PinItemService;
use App\Services\PollWav\PollService\PollServiceImplV4;
use App\V4\Models\Mikrowav;
use App\V4\Repositories\Bookmarks\BookmarkRepositoryV4;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use stdClass;
use App\Models\FbUser;
use App\Models\NwUser;
use App\V4\Models\NwUser as NwUserV4;
use App\V4\Models\NwWebUser;

class ContentRepository
{

    public const CONTENT_MODE_FULL = 1;
    public const CONTENT_MODE_BRIEF = 2;
    public const CONTENT_MODE_SUPER_BRIEF = 3;
    public const SHOW_ARTICLE_POJECTS = ['newswav', 'headliner', 'wavmaker','ugc'];

    protected $importantPublishers;
    protected $limit;
    public function __construct()
    {
        $this->importantPublishers = nw_bunker('publisher', 'important_publishers'); // [263,262,20,96,97,101,409]
        $this->excludeChannelIds = nw_bunker('channel', 'exclude_from_hot_feed_ids');
        $this->imageproxykey = config('app.image_proxy_key');
    }

    public function getContentFromPool($contentGroupsToSkip, $languages, $excludedPublishers, $excludedContents, $afterPublishedDate = false, $limit = false, $type = false, $sort = 'latest', $onlyTopics = false, $onlyPublishers = false): Collection
    {
        $timeFrame = nw_bunker('feed_service', 'time_frame', ['a' => [], 'v' => []]);
        $q = DB::table('content_pool')
            ->whereIn('language', $languages)
            ->where('disabled', 0)
            // todo to be removed
            // ->whereNotIn('content_pool.similarity_group_slug', $contentGroupsToSkip)
            // ->where(function ($query) use( $timeFrame, $afterPublishedDate ){
            //     if($afterPublishedDate) return $query;
            //     foreach($timeFrame['a'] as $hours => $topics){
            //         $query =  $query->orWhere(function($query) use ($topics, $hours){
            //             return $query->whereIn('topic', $topics)
            //                 ->where('type', "a")
            //                 ->where(
            //                     'published_date',
            //                     ">=",
            //                     time() - ($hours * 60 * 60)
            //                 );
            //         });
            //     }
            //
            //     foreach($timeFrame['v'] as $hours => $topics){
            //         $query =  $query->orWhere(function($query) use ($topics, $hours){
            //             return $query->whereIn('topic', $topics)
            //                 ->where('type', "v")
            //                 ->where(
            //                     'published_date',
            //                     ">=",
            //                     time() - ($hours * 60 * 60)
            //                 );
            //         });
            //     }
            //
            //     return $query;
            // })
            ->when($afterPublishedDate, function ($query, $afterPublishedDate) {
                return $query->where('published_date', '<', $afterPublishedDate);
            })
            ->when(!$afterPublishedDate, function ($query) {
                return $query->where('published_date', '<', time());
            })
            ->when($onlyTopics, function ($query, $onlyTopics) {
                return $query->whereIn('topic', $onlyTopics);
            })
            ->when(!$onlyTopics, function ($query) {
                return $query->where('topic', '!=', -1);
            })
            ->when($onlyPublishers, function ($query, $onlyPublishers) {
                return $query->whereIn('publisher', $onlyPublishers);
            })
            ->when($limit, function ($query, $limit) use($sort) {
                if($sort == 'latest')
                    return $query->limit($limit)->orderByDesc('published_date');

                return $query->limit($limit)->orderByDesc('score');
            })
            ->when($type, function ($query, $type) {
                if ($type == "article") {
                    return $query->where('type', "a");
                }
                if ($type == "video") {
                    return $query->where('type', "v");
                }
                return $query;
            });

        $data = new Collection([]);
        if ($sort == 'latest'){
            $data = DB::table(DB::raw("({$q->toSql()}) as sub"))->mergeBindings($q)->groupBy('similarity_group_slug')->orderByDesc('published_date')->get();
        } else {
            $innerQuery = clone $q;
            $innerQuery = getEloquentSqlWithBindings($innerQuery->selectRaw(DB::raw("similarity_group_slug, MAX( score ) AS score"))->groupBy('similarity_group_slug'));
            $q->select('content_pool.unique_id', 'content_pool.similarity_group_slug', 'content_pool.score', 'content_pool.topic',  'content_pool.publisher',  'content_pool.channel')
                ->join(DB::raw("({$innerQuery}) as b"),
                    function($join)
                    {
                        $join->on('content_pool.similarity_group_slug', '=', 'b.similarity_group_slug')
                            ->whereRaw(DB::raw('content_pool.score = b.score'));
                    })
                ->groupBy('content_pool.similarity_group_slug')
                ->orderBy('content_pool.score', 'desc');
            $data = $q->get();
        }

        $data = $data
            ->whereNotIn('channel', explode(',', getDisabledChannelsFromCache()))
            ->whereNotIn('publisher', $excludedPublishers)
            ->whereNotIn('unique_id', $excludedContents);

        return $data;
    }

    public function getClipFromPool($contentGroupsToSkip, $languages, $excludedPublishers, $excludedContents, $afterPublishedDate = false, $limit = false, $type = false, $sort = 'latest', $onlyTopics = false, $onlyPublishers = false): Collection
    {
        $articlesRepo = app(ArticleRepository::class);
        $timeFrame = nw_bunker('feed_service', 'time_frame', ['a' => [], 'v' => []]);
        $q = DB::table('content_pool')
            ->whereIn('language', $languages)
            ->where('disabled', 0)
            ->whereNotIn('content_pool.similarity_group_slug', $contentGroupsToSkip)
            ->where('topic', '!=', -1)
            ->where(function ($query) use( $timeFrame, $afterPublishedDate ){
                if($afterPublishedDate) return $query;
                foreach($timeFrame['a'] as $hours => $topics){
                    $query =  $query->orWhere(function($query) use ($topics, $hours){
                        return $query->whereIn('topic', $topics)
                            ->where('type', "a")
                            ->where(
                                'published_date',
                                ">=",
                                time() - ($hours * 60 * 60)
                            );
                    });
                }

                foreach($timeFrame['v'] as $hours => $topics){
                    $query =  $query->orWhere(function($query) use ($topics, $hours){
                        return $query->whereIn('topic', $topics)
                            ->where('type', "v")
                            ->where(
                                'published_date',
                                ">=",
                                time() - ($hours * 60 * 60)
                            );
                    });
                }

                return $query;
            })
            ->when($afterPublishedDate, function ($query, $afterPublishedDate) {
                return $query->where('published_date', '<', $afterPublishedDate);
            })
            ->when(!$afterPublishedDate, function ($query, $afterPublishedDate) {
                return $query->where('published_date', '<', time());
            })
            ->when($onlyTopics, function ($query, $onlyTopics) {
                return $query->whereIn('topic', $onlyTopics);
            })
            ->when($onlyPublishers, function ($query, $onlyPublishers) {
                return $query->whereIn('publisher', $onlyPublishers);
            })
            ->when($limit, function ($query, $limit) use($sort) {
                if($sort == 'latest')
                    return $query->limit($limit)->orderByDesc('published_date');

                return $query->limit($limit)->orderByDesc('score');
            })
            ->when($type, function ($query, $type) {
                if ($type == "article") {
                    return $query->where('type', "a");
                }
                if ($type == "video") {
                    return $query->where('type', "v");
                }
                return $query;
            });

        if ($sort == 'latest'){
            return DB::table(DB::raw("({$q->toSql()}) as sub"))->mergeBindings($q)->groupBy('similarity_group_slug')->orderByDesc('published_date')->get();
        } else {
            $innerQuery = clone $q;
            $innerQuery = getEloquentSqlWithBindings($innerQuery->selectRaw(DB::raw("similarity_group_slug, MAX( score ) AS score"))->groupBy('similarity_group_slug'));
            $q->select('content_pool.unique_id', 'content_pool.similarity_group_slug', 'content_pool.score', 'content_pool.topic')
                ->join(DB::raw("({$innerQuery}) as b"),
                    function($join)
                    {
                        $join->on('content_pool.similarity_group_slug', '=', 'b.similarity_group_slug')
                            ->whereRaw(DB::raw('content_pool.score = b.score'));
                    })
                ->groupBy('content_pool.similarity_group_slug')
                ->orderBy('content_pool.score', 'desc');
            return $q->get();
        }
    }

    /**
     * Repository layer should only contains DB query ONLY
     * Prepare the query to retrieve hot contents
     *
     * @param array $excludeIds content ids to be excluded
     *
     * @param array $excludePubIds query will not return the content under these publisher ids
     *
     * @param array $languages content that match to provided language
     *
     * @param array $types supports a = article, v = video, p = podcast. to determine which content type to retrieve
     *
     * @param array $importantPublisherIds contains array of important publisher ids to make sure important publishers that updates their published date will still appear
     *
     * @param array $excludeSlugs contains array of excluded group slugs from previous page
     *
     * @param ?string $firebaseId is the firebase id to be used to exclude contents that the particular firebase id have no interest in
     */
    public function getHotQuery(
        array $excludeIds, array $excludePubIds, array $languages, array $types, $importantPublisherIds = [],
        $excludeSlugs = [], ?string $firebaseId = null, ?bool $isNativeOnly = false
    ) {

        $q = DB::table('content_pool')
                ->leftJoin('channels','content_pool.channel','=','channels.id')
                ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','content_pool.unique_id')

                ->where('content_pool.created_at', '<=', Carbon::now())
                ->whereIn('content_pool.type', $types)
                ->where('content_pool.published_date', '<=', Carbon::now()->timestamp)
                ->where('content_pool.published_date', '>=', Carbon::now()->subHours(16)->timestamp) //follow app query logic
                ->where('content_pool.score', '>=', nw_bunker('query', 'hot_threshold', 5))
                ->whereNotIn('content_pool.unique_id', $excludeIds)
                ->whereNotIn('content_pool.similarity_group_slug', $excludeSlugs)
                ->where('channels.listing_enabled',1)
                ->whereIn('content_pool.language', $languages)
                ->whereNull('disabled_contents.unique_id')
                ->where(function ($query) use ($importantPublisherIds){
                    $query->where('content_created_at', '>=', Carbon::now()->subHours(24)) //follow app query logic
                    ->orWhere(function($q) use ($importantPublisherIds){
                        $q->whereIn('publisher', $importantPublisherIds);
                    });
                })
                ->when($firebaseId, function ($query) use ($firebaseId){
                    return $this->excludeNotInterestedQuery($query, 'content_pool.unique_id', $firebaseId);
                })
                ->when($isNativeOnly, function ($query) {
                    // used mainly for indexing native articles only
                    return $query->where('channels.reader_view_only', 1);
                });
        //clone query and make as subquery
        $innerQuery = clone $q;
        $innerQuery = getEloquentSqlWithBindings($innerQuery->selectRaw(DB::raw("similarity_group_slug, MAX( score ) AS score"))->groupBy('similarity_group_slug'));
        return $q->select('content_pool.unique_id', 'content_pool.similarity_group_slug', 'content_pool.score')
        ->join(DB::raw("({$innerQuery}) as b"),
        function($join)
        {
            $join->on('content_pool.similarity_group_slug', '=', 'b.similarity_group_slug')
                ->whereRaw(DB::raw('content_pool.score = b.score'));
        })
        ->groupBy('similarity_group_slug')
        ->orderBy('content_pool.score', 'desc')
        ->orderBy('content_pool.updated_at');
    }

    public function getHotQueryOLD(array $excludeIds, array $excludePubIds, array $languages, array $types, $importantPublisherIds = []) {
        return DB::table('content_pool')
                ->leftJoin('channels','content_pool.channel','=','channels.id')
                ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','content_pool.unique_id')
                ->select('content_pool.unique_id', 'content_pool.published_date', 'content_pool.score')
                ->distinct('content_pool.unique_id')
                ->where('content_pool.created_at', '<=', Carbon::now())
                ->whereIn('content_pool.type', $types)
                ->where('content_pool.published_date', '<=', Carbon::now()->timestamp)
                ->where('content_pool.published_date', '>=', Carbon::now()->subHours(16)->timestamp) //follow app query logic
                ->where('content_pool.score', '>=', nw_bunker('query', 'hot_threshold', 5))
                ->whereNotIn('content_pool.unique_id', $excludeIds)
                ->where('channels.listing_enabled',1)
                ->whereIn('content_pool.language', $languages)
                ->whereNull('disabled_contents.unique_id')
                ->where(function ($query) use ($importantPublisherIds){
                    $query->where('content_created_at', '>=', Carbon::now()->subHours(24)) //follow app query logic
                    ->orWhere(function($q) use ($importantPublisherIds){
                        $q->whereIn('publisher', $importantPublisherIds);
                    });
                })
                ->orderBy('content_pool.score', 'desc')
                ->orderBy('content_pool.updated_at');
    }

    /**
     * Repository layer should only contains DB query ONLY
     * Prepare the query to retrieve hot contents
     *
     * @param array $excludeIds content ids to be excluded
     *
     * @param array $excludePubIds query will not return the content under these publisher ids
     *
     * @param array $languages content that match to provided language
     *
     * @param array $types supports a = article, v = video, p = podcast. to determine which content type to retrieve
     *
     * @param int $publishedSince to get the latest article starting from published date
     *
     * @param array $topicIds ids to include topic
     *
     * @param array $onlyPublisherIds to query just the publisher ids
     *
     * @param ?string $firebaseId firebase id to be used to exclude non interested contents
     */
    public function getLatestQuery(
        array $excludeIds, array $excludePubIds, array $languages, array $types, int $publishedSince,
        $topicIds = [], $onlyPublisherIds = [], ?string $firebaseId = null, ?bool $isNativeOnly = false
    ) {
        $q = DB::table('content_pool')
            ->select('content_pool.unique_id', 'content_pool.published_date')
            ->leftJoin('channels','content_pool.channel','=','channels.id')
            ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','content_pool.unique_id')
            ->distinct('content_pool.unique_id')
            ->whereIn('content_pool.type', $types)
            ->where('content_pool.published_date', '<=', $publishedSince)
            ->where('content_pool.created_at', '<', Carbon::createFromTimestamp($publishedSince)->format('Y-m-d H:i:s'))
            ->where('channels.listing_enabled',1)
            ->whereIn('content_pool.language', $languages)
            ->whereNotIn('content_pool.unique_id', $excludeIds)
            ->whereNotIn('content_pool.publisher', $excludePubIds)
            ->whereNull('disabled_contents.unique_id')
            ->when($firebaseId, function ($query) use ($firebaseId){
                return $this->excludeNotInterestedQuery($query, 'content_pool.unique_id', $firebaseId);
            })
            ->when($isNativeOnly, function ($query) {
                // used mainly for indexing native articles only
                return $query->where('channels.reader_view_only', 1);
            })
            ->whereNotIn('content_pool.publisher', [nw_bunker('poll', 'pollwav_publisher_id', 369)])
            ->orderBy('content_pool.published_date', 'DESC')
            ->orderBy('content_pool.created_at', 'desc');

        if (!empty($onlyPublisherIds) || !empty($topicIds)) {
            $q->where(function($q) use ($onlyPublisherIds, $topicIds) {
                if (!empty($onlyPublisherIds)) {
                    $q->orWhereIn('content_pool.publisher',$onlyPublisherIds);
                }

                if (!empty($topicIds)) {
                    $q->orWhereIn('content_pool.topic', $topicIds);
                }
            });

        }
        return DB::table(DB::raw("({$q->toSql()}) as sub"))->mergeBindings($q)->groupBy('unique_id')->orderByDesc('published_date');
    }

    public function getLatestContentUniqueIds(int $limit): array {
        return DB::table('content_pool')
            ->select('content_pool.unique_id')
            ->leftJoin('channels','content_pool.channel','=','channels.id')
            ->distinct('content_pool.unique_id')
            ->whereIn('content_pool.type', ['a','v'])
            ->where('content_pool.published_date', '<=', Carbon::now()->timestamp)
            ->where('channels.listing_enabled',1)
            ->where('channels.reader_view_only', 1)
            ->orderBy('content_pool.published_date', 'DESC')
            ->limit($limit)
            ->get()
            ->pluck('unique_id')
            ->toArray();
    }

    public function getGenericLatestQuery() {
        $q = DB::table('content_pool')
            ->select('content_pool.unique_id', 'content_pool.published_date', 'content_pool.language', 'content_pool.publisher', 'content_pool.channel', 'content_pool.type')
            ->leftJoin('channels','content_pool.channel','=','channels.id')
            ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','content_pool.unique_id')
            ->distinct('content_pool.unique_id')
            ->where('content_pool.published_date', '<=', time())
            ->where('content_pool.created_at', '<', Carbon::createFromTimestamp(time())->format('Y-m-d H:i:s'))
            ->where('channels.listing_enabled',1)
            ->whereNull('disabled_contents.unique_id')
            ->orderBy('content_pool.published_date', 'DESC')
            ->orderBy('content_pool.created_at', 'desc');
        return DB::table(DB::raw("({$q->toSql()}) as sub"))->mergeBindings($q)->groupBy('unique_id')->orderByDesc('published_date');
    }

    public function getFeedContentQuery(array $feedIds, array $languages, array $contentTypes, array $excludeIds, array $excludePubIds, ?int $last = null, ?string $firebaseId) {
        return  DB::table('segment_feed_articles')->whereIn('segment_feed_articles.feed_id', $feedIds)
                ->leftJoin('channels','segment_feed_articles.channel_id','=','channels.id')
                ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','segment_feed_articles.article_unique_id')
                ->whereNull('disabled_contents.unique_id')
                ->where('channels.listing_enabled',1)
                ->where('segment_feed_articles.published_date','<=', Carbon::now()->format('Y-m-d H:i:s'))
                ->whereIn('segment_feed_articles.content_type', $contentTypes)
                ->whereIn('segment_feed_articles.language', $languages)
                ->whereNotIn('segment_feed_articles.article_unique_id',$excludeIds)
                ->whereNotIn('channels.publisher_id', $excludePubIds)
                ->select('segment_feed_articles.id', 'article_unique_id as unique_id', 'published_date')
                ->when($last, function ($q) use ($last){
                    $q->where('segment_feed_articles.id', '<=', $last);
                })
                ->when($firebaseId, function ($query) use ($firebaseId){
                    return $this->excludeNotInterestedQuery($query, 'segment_feed_articles.article_unique_id', $firebaseId);
                })
                ->orderBy('published_date', 'DESC');
    }

    public function getFeedForIndex($languages, $page, $feed, $limit, $last = null, $contentTypes = ['a','v']){

        $pinService = app(PinItemService::class);
        $pinnedArticles = $pinService->getPinned($feed->id, ['article', 'video'], $languages);
        $uniqueIdColumn = 'article_unique_id';
            $query = DB::table('segment_feed_articles')
                        ->leftJoin('channels','segment_feed_articles.channel_id','=','channels.id')
                        ->whereIn('segment_feed_articles.feed_id', explode(",", $feed->relationship_ids))
                        ->where('segment_feed_articles.enabled', 1)
                        ->whereIn('segment_feed_articles.content_type', $contentTypes)
                        ->where('channels.reader_view_only', 1)
                        ->select('segment_feed_articles.article_unique_id as unique_id', 'segment_feed_articles.published_date');

        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('segment_feed_articles.channel_id', \explode(',', $disabledChannels));
        }

        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('segment_feed_articles.channel_id', \explode(',', $disabledChannels));
        }

        $query = $query
                    ->whereIn('segment_feed_articles.language', $languages)
                    ->when($last, function ($query) use (&$last) {
                        //return $query->where('published_date', '<', Carbon::createFromTimestamp($last)->format('Y-m-d H:i:s'));
                        return $query->where('segment_feed_articles.published_date', '<=', Carbon::createFromTimestamp($last)->format('Y-m-d H:i:s'));
                    })
                    ->when(!$last, function ($query) {
                        return $query->where('segment_feed_articles.published_date', '<=', Carbon::now());
                    })
                    ->distinct()
                    ->orderBy('segment_feed_articles.published_date', 'DESC');

        //avoid fetching the pinned unique_ids
        $query = $query->whereNotIn($uniqueIdColumn, $pinnedArticles);
        if (count($pinnedArticles) > 0 && $page == 1) {
            $limit = $limit - count($pinnedArticles);
        }

        if ($page > 1) {
            $query = $query->offset(($page - 1) * $limit - (count($pinnedArticles)));
        }
        $articles = json_decode($query->take($limit)->get()->toJson());
        $ids = array();
        foreach ($articles as $d) {
            $ids[] = $d->unique_id;
        }
        $fields = (object)[];
        if ($page == 1){
            $last = count($ids) > 0 ? Carbon::parse($articles[0]->published_date)->timestamp : null;
        }
        $url = count($ids) > 0 ? urldecode(route('v4-v1-feed-api-web', [
            'page' => $page + 1,
            'languages' => 'en,ms',
            'last' => $last
        ])) : "";
        $fields->next_page_url = $url;
        //put it in last atribute so website can use it
        $fields->last = $last;
        if ($page <= 1 && $pinnedArticles && count($pinnedArticles) > 0) {

            $ids = array_merge($pinnedArticles, $ids);

            $fields->data = $ids;
        } else {
            $fields->data = $ids;
        }
        return $fields;

    }

    public function getFeedBySlug($slug){
        $feed = DB::table('segment_feed2')->where('slug', $slug)->first();

        if (!$feed){
            //throw exception invalid slug
            // add later
            abort(400, "invalid slug!!!");
        }

        return $feed;
    }

    public function getPublisherArticles(
        ?array $languages,
        $publisher,
        int $limit = 30
    ) {
        $query = DB::table('content_pool')
            ->select('unique_id', 'published_date')
            ->distinct()
            ->whereIn('language', $languages)
            ->where('publisher', $publisher)
            ->orderBy('published_date', 'DESC');

        $firebaseId = Auth::user()->getFirebaseId();
        if ($firebaseId) {
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if ($reportedArticles && count($reportedArticles)) {
                $query = $query->whereNotIn('unique_id', $reportedArticles);
            }
        }

        $articles = json_decode($query->take($limit)->simplePaginate($limit)->toJson());
        $ids = array();
        foreach ($articles->data as $d) {
            $ids[] = $d->unique_id;
        }
        $articles->data = $ids;
        $articles->next_page_url = urldecode($articles->next_page_url);
        return $articles;
    }

    public function getPublisherArticlesWeb(
        ?array $languages,
        $publisher,
        int $limit = 30
    ) {

        $specialPublisherIds = cache("newswav_wavmaker_headliner_ids", function(){
            $publisherRepo = app(PublisherRepository::class);
            return $publisherRepo->getPublisherIdsByProject(['newswav', 'wavmaker', 'headliner','ugc']);
        });

        $query = DB::table('predictions')
            ->select('unique_id', 'published_date')
            ->distinct()
            ->whereIn('language', $languages)
            ->where('publisher_id', $publisher)
            ->where('published_date', '<=', Carbon::now())
            ->orderBy('published_date', 'DESC');

        if (!in_array($publisher, $specialPublisherIds)){
            $query->where('published_date', '>=', Carbon::now()->subDays(3));
        }

        $firebaseId = Auth::user()->getFirebaseId();
        if ($firebaseId) {
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if ($reportedArticles && count($reportedArticles)) {
                $query = $query->whereNotIn('unique_id', $reportedArticles);
            }
        }

        $articles = json_decode($query->take($limit)->simplePaginate($limit)->appends(['languages' => implode(",", $languages)])->toJson());
        $ids = array();
        foreach ($articles->data as $d) {
            $ids[] = $d->unique_id;
        }
        $articles->data = $ids;
        $articles->next_page_url = urldecode($articles->next_page_url);
        return $articles;
    }

    public function getPollsWeb(
        int $limit = 30
    ) {

        $query = DB::connection('poll_cluster')->table('polls')
            ->select('article_unique_id as unique_id', 'published_date')
            ->distinct()
            ->where('published_date', '<=', Carbon::now()->timestamp)
            ->where('status', 'active')
            ->orderBy('published_date', 'DESC');

        $articles = json_decode($query->take($limit)->simplePaginate($limit)->toJson());
        $ids = array();
        foreach ($articles->data as $d) {
            $ids[] = $d->unique_id;
        }
        $articles->data = $ids;
        $articles->next_page_url = urldecode($articles->next_page_url);
        return $articles;
    }

    public function getTopicArticles(
        ?array $languages,
        $topic,
        int $limit = 30
    ) {
        $query = DB::table('content_pool')
            ->select('unique_id', 'published_date')
            ->distinct()
            ->whereIn('language', $languages)
            ->where('topic', $topic)
            ->orderBy('published_date', 'DESC');

        $firebaseId = Auth::user()->getFirebaseId();
        if ($firebaseId) {
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if ($reportedArticles && count($reportedArticles)) {
                $query = $query->whereNotIn('unique_id', $reportedArticles);
            }
        }

        $articles = json_decode($query->take($limit)->simplePaginate($limit)->toJson());
        $ids = array();
        foreach ($articles->data as $d) {
            $ids[] = $d->unique_id;
        }
        $articles->data = $ids;
        $articles->next_page_url = urldecode($articles->next_page_url);
        return $articles;
    }

    public function getContentBody($contentIds, int $mode = 1, string $pinFeed = null, array $languages = null): Collection
    {
        /** @var NwUser|FbUser|NwUserV4|NwWebUser $user */
        $user = Auth::user();
        $contentIds = collect($contentIds);
        // remove content ids with empty string
        $contentIds = $contentIds->filter(function ($item){
            return !empty($item);
        });

        if (count($contentIds) < 1) {
            return collect();
        }
        $articleIds = []; //array of ids to fetch content
        $videoIds = [];
        $pollIds = [];
        $mikrowavIds = [];
        $podcastIds = [];
        $contentInfo = collect();
        $pinService = app(PinItemService::class);
        //get the topics
        $contents = $contentIds->map(function ($item) use (&$articleIds, &$videoIds, &$pollIds, &$mikrowavIds, &$podcastIds) {
            $newItem = new stdClass;
            $newItem->isRecommended = false;
            $split = explode(';', $item, 2);
            if (isset($split[0])) {
                $newItem->uniqueId = $split[0];
            }
            if (isset($split[1])) {
                //keep track_code to deliver to client
                $newItem->trackCode = $split[1];
                $parts = explode(':', explode(';', $newItem->trackCode)[0]);

                if(isset( $parts[2]) && $parts[2] == 'RN'){
                    $newItem->isRecommended = true;
                }
            }

            $newItem->contentType = $this->getType($newItem->uniqueId);

            if ($newItem->contentType == "article") {
                $articleIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "video") {
                $videoIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "podcast") {
                $podcastIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "poll") {
                $pollIds[] = $newItem->uniqueId;
                $articleIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "mikrowav") {
                $mikrowavIds[] = $newItem->uniqueId;
            }
            return $newItem;
        });

        $pollsCount = $contents->where('contentType', 'PO')->count();
        if ($pollsCount > 0) {
            $pollService = app(PollServiceImplV4::class);
            $polls = $pollService->getPollToInject($pollsCount);
            $contents = $contents->map(function ($item) use (&$polls, &$pollIds, &$articleIds, &$podcastIds) {
                if ($item->contentType == "PO") {
                    $item->contentType = "poll";
                    $item->uniqueId = array_shift($polls);
                    $pollIds[] = $item->uniqueId;
                    $articleIds[] = $item->uniqueId;
                }
                return $item;
            });
        }
        //get body contents;
        $articleContents = collect();
        $mikrowavContents = collect();
        $videoContents = collect();
        $podcastContents = collect();
        $pollContents = collect();
        $pinnedArticles =  [];
        $pinnedVideos =  [];
        $pinnedPodcasts = [];
        if (count($articleIds) > 0) {
            $articleContents = $this->getArticles($articleIds)->keyBy('unique_id');
            $pinnedArticles = $pinFeed ? $pinService->getPinned($pinFeed, ['article', 'highlight'], $languages) : $pinnedArticles;
        }
        if (count($videoIds) > 0) {
            $videoContents = $this->getVideos($videoIds)->keyBy('unique_id');
            $pinnedVideos = $pinFeed ? $pinService->getPinned($pinFeed, ['video', 'highlight'], $languages) : $pinnedVideos;
        }
        if (count($podcastIds) > 0) {
            $podcastContents = $this->getPodcasts($podcastIds)->keyBy('unique_id');
            $pinnedPodcasts = $pinFeed ? $pinService->getPinned($pinFeed, ['podcast', 'highlight'], $languages) : $pinnedPodcasts;
        }
        if (count($mikrowavIds) > 0) {
            $mikrowavContents = Mikrowav::join('userLogin', 'mikrowavs.user_id', '=', 'userLogin.id')
                ->select('mikrowavs.*')
                ->whereIn('mikrowavs.unique_id', $mikrowavIds)
                ->whereNull('userLogin.deleted_at')
                ->get()
                ->keyBy('unique_id');
        }

        $userFirebaseId = $user->getFirebaseId();
        if (count($pollIds) > 0 && !is_null($userFirebaseId)) {
            /** @var PollServiceImplV4 $pollService */
            $pollService = app(PollServiceImplV4::class);
            $pollContents = $pollService->getPollsByArticleIds($pollIds, $userFirebaseId);
        }
        $bookmarkRepository = app(BookmarkRepositoryV4::class);
        $bookmarkedContents = is_null($userFirebaseId) ? collect([]) : $bookmarkRepository->getByContentIds($userFirebaseId, array_merge($articleIds, $videoIds, $podcastIds))->keyBy('entity_id');
        //retrieve topic and language fromc content_pool table
        $contentInfo = collect();
        $contentInfo = $contentInfo->merge($this->getContentInfo($articleIds, 'article'));
        $contentInfo = $contentInfo->merge($this->getContentInfo($videoIds, 'video'));
        $contentInfo = $contentInfo->merge($this->getContentInfo($podcastIds, 'podcast'));
        $topicRepo = app(TopicRepository::class);
        $topicsData = $topicRepo->getAllTopics();
        $mainLanguage = strtolower($user->getMainLanguage());
        //map content
        $contents = $contents->map(function ($item) use (&$articleContents, &$mikrowavContents, &$videoContents, &$pollContents, &$podcastContents, $mode, &$contentInfo, &$topicsData, &$mainLanguage, $pinnedArticles, $pinnedVideos, $pinnedPodcasts, $bookmarkedContents) {
            if ($item->contentType === 'mikrowav') {
                $content = $mikrowavContents->first(function ($mikrowavContent) use ($item) {
                    return strtolower($mikrowavContent->unique_id) === strtolower($item->uniqueId);
                });
                if ($content === null || $content->user === null || $content->user->userProfile === null) {
                    return null;
                }
                $item = $this->decorateMikrowavContent($item, $content);
            }
            if (in_array($item->contentType, ["article", "poll"])) {
                if (!$item->uniqueId) return $item;
                $content = $articleContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->description;
                $item->isNative = (bool) $content->readerViewOnly;
                $item->publishedDate = $content->publishedDate;
                $item->deleted = $content->deleted ?? 0;
                $item->hideAds = $content->hideAds;
                $item->stats = (object)[
                    "views" => $content->viewCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->shares,
                    "reactions" =>  $content->articleReaction,
                    "userReactionType" => $content->userReactionType,
                    "adEnable" => (int) $content->adEnable,
                    "readerViewOnly" => (bool)  $content->readerViewOnly,
                    "showWebsite" => (int) $content->showWebsite,
                    "enableJS" => (int) $content->enableJS,
                    "boostReaderView" => (int)  $content->boostReaderView,
                ];

                $shareImageUrl = !empty($content->mediaArray) ? (!empty($content->mediaArray[0]['url']) ? $content->mediaArray[0]['url'] : $content->mediaArray[0]['thumbnail'] ): null;

                unset($item->stats->articleID);
                $item->meta = (object)[
                    "article" => (object)[
                        "hideAds" => $content->hideAds,
                        "pinned" => in_array($item->uniqueId, $pinnedArticles) ? 1 : 0,
                        "showOriginalArticle" => in_array($content->publisher->project, self::SHOW_ARTICLE_POJECTS) ? false : true,
                        "content" => $content->html,
                        "language" => $content->language,
                        "url" => $content->url,
                        "permalink" => $content->permalink,
                        "thumbnailUrl" => !empty($content->mediaArray) ? $content->mediaArray[0]['thumbnail'] : null,
                        "originalUrl" => !empty($content->mediaArray) ? (!empty($content->mediaArray[0]['url']) ? $content->mediaArray[0]['url'] : $content->mediaArray[0]['thumbnail']) : null,
                        "shareImageUrl" => $shareImageUrl,
                        "media" => $content->mediaArray
                    ],
                ];
                $item->thumbnailUrl = $item->contentType === "article" ? $shareImageUrl : nw_bunker('poll', "poll_thumbnail_$mainLanguage");

                if ($mode == static::CONTENT_MODE_BRIEF) {
                    unset($item->meta->article->content);
                }

                if ($mode == static::CONTENT_MODE_SUPER_BRIEF) {
                    //unset stats
                    $tempViews = $item->stats->views;
                    unset($item->stats);
                    $item->stats = (object)[
                        'views' => $tempViews
                    ];

                    unset($item->meta->article->content);
                }
            }

            if ($item->contentType == "video") {
                $content = $videoContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->long_desc ?? $content->description;
                $item->thumbnailUrl = isset($content->images[0]) ? $content->images[0]["url"] : null;
                $item->isNative = true; //hardcode to 1 for now?
                $item->publishedDate = Carbon::createFromTimestamp($content->published_date)->format('Y-m-d H:i:s');
                $item->deleted = $content->deleted;
                $item->hideAds = $content->hideAds;
                $item->stats = (object)[
                    "views" => (int) $content->viewsCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->sharesCount,
                    "reactions" =>  $content->reactions,
                    "userReactionType" => $content->userReactionType,
                    "readerViewOnly" => true,
                ];
                unset($item->stats->articleID);
                $item->meta = (object)[
                    "video" => (object)[
                        "hideAds" => $content->hideAds,
                        "playerType" => $content->player_type,
                        "pinned" => in_array($item->uniqueId, $pinnedVideos) ? 1 : 0,
                        "url" => $content->video_url_1 ?? $content->video_url_2,
                        "permalink" => $content->permalink,
                        "language" => $content->language,
                        "videoUrl" => $content->page_url ?? "",
                        "thumbnailUrl" => isset($content->images[0]) ? $content->images[0]["url"] : null,
                        "first_frame_thumbnail_url" => $content->first_frame_thumb_url,
                        "duration" => $content->video_duration,
                        "aspectRatio" => $content->aspect_ratio,
                        "mimeType" => $content->mime_type,
                        "playerVideoSrc" => $content->type == "youtube" ? $content->youtube_link : ($content->video_url_1 ?? $content->video_url_2),
                        "isLive" => $content->is_live == 1 ? true : false
                    ],
                ];
            }
            if ($item->contentType == "podcast") {
                $content = $podcastContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->long_desc ?? $content->description;
                $item->thumbnailUrl = isset($content->images[0]) ? $content->images[0]->url : null;
                $item->isNative = true; //hardcode to 1 for now?
                $item->publishedDate = Carbon::createFromTimestamp($content->published_date)->format('Y-m-d H:i:s');
                $item->deleted = $content->deleted;
                $item->hideAds = $content->hideAds;
                $item->stats = (object)[
                    "views" => (int) $content->viewsCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->sharesCount,
                    "reactions" =>  $content->reactions,
                    "userReactionType" => $content->userReactionType,
                    "readerViewOnly" => true,
                ];
                unset($item->stats->articleID);
                $item->meta = (object)[
                    "podcast" => (object)[
                        "hideAds" => $content->hideAds,
                        "pinned" => in_array($item->uniqueId, $pinnedPodcasts) ? 1 : 0,
                        "url" => $content->pod_url,
                        "permalink" => $content->permalink,
                        "language" => $content->language,
                        "showName" => $content->show_name,
                        "podcastUrl" => $content->url,
                        "thumbnailUrl" => isset($content->images[0]) ? $content->images[0]->url : null,
                        "duration" => $content->pod_duration
                    ],
                ];
            }
            if (in_array($item->contentType, ['article', 'video', 'podcast', 'poll'])){
                switch ($item->contentType){
                    case 'article':
                        $pinnedIds = $pinnedArticles;
                        break;
                    case 'video':
                        $pinnedIds = $pinnedVideos;
                        break;
                    case 'podcasts':
                        $pinnedIds = $pinnedVideos;
                    default:
                        $pinnedIds = [];
                }
                $item->isBookmarked = $bookmarkedContents->has($item->uniqueId);
                $item->publisher = (object)[
                    "id" => $content->publisher->id,
                    "name" => $content->publisher->publisherName,
                    "logo" => $content->publisher->publisherLogoURL,
                    "isVerified" => (bool) $content->publisher->verified,
                    "isFollowing" => (bool) $content->publisher->is_following,
                    "project" => $content->publisher->project,
                    'enabled' => $content->publisher->enabled,
                    'websiteUrl' => $content->publisher->websiteUrl,
                    'permalink' => $content->publisher->permalink,
                    'isNewswav' => in_array($content->publisher->id, nw_bunker('publisher', 'newswav_publishers', [])),
                    'showFollowButton' => !in_array($content->publisher->id, nw_bunker('publisher', 'hide_follow_button', [])),
                    'showHideButton' => in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_hide_button', [])) ? true : false),
                ];
                $item->showHideButton = in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_content_hide_button', [])) ? true : false);
                $item->showNotInterestedButton = in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_not_interested_button', [])) ? true : false);
                unset($pinnedIds);
            }

            //assign topic and language to meta
            if (in_array($item->contentType, ['article', 'video', 'podcast'])) {
                $currentContent = $contentInfo->get($item->uniqueId);
                if ($item->contentType == 'podcast'){
                    $currentTopic = $currentContent ? $currentContent->pluck('topic')->toArray() : [];
                    $topics = $topicsData->whereIn('id', array_map(function ($item){
                        if ($item == 18){
                            return 1;
                        }
                        return $item;
                    }, $currentTopic));
                }else{
                    $topics = $topicsData->whereIn('id', $currentContent ? $currentContent->pluck('topic') : null);
                }
                if ($topics) {
                    $topicArray = [];
                    foreach ($topics as $topic) {
                        $new = new stdClass;
                        $new->id = $topic->id;
                        switch ($mainLanguage) {
                            case "en":
                                $new->name = $topic->nameEN;
                                break;
                            case "ms":
                                $new->name = $topic->nameMS;
                                break;
                            case "zh":
                                $new->name = $topic->nameZH;
                                break;
                            default:
                                $new->name = $topic->nameEN;
                        }
                        $new->subscribed = (int)$topic->is_following;
                        $topicArray[] = $new;
                    }
                    $item->meta->{$item->contentType}->topics = $topicArray;
                }
            }

            if ($item->contentType == "poll") {
                $poll = $pollContents->get($item->uniqueId)->first();
                $item->meta->poll = $poll;
                $pollPrefix = nw_bunker('poll', "prefix_poll_$mainLanguage", "[POLL]");
                $item->title =  "$pollPrefix {$item->meta->poll->question}" ;
                unset($item->meta->article);
            }

            return $item;
        });

        return $contents->filter(function ($item) {
            return $item !== null && $item->uniqueId;
        });
    }

    public function getContentBodyWeb(
        $contentIds,
        int $mode = 1,
        string $pinFeed = null,
        array $languages = null,
        bool $showContent = true
    ): Collection {

        // remove content ids with empty string
        $contentIds = collect($contentIds)->filter(function ($item){
            return !empty($item);
        });

        $articleIds = []; //array of ids to fetch content
        $videoIds = [];
        $pollIds = [];
        $podcastIds = [];
        $mikrowavIds = [];
        $contentInfo = collect();
        $pinService = app(PinItemService::class);
        //get the topics
        $contents = $contentIds->map(function ($item) use (&$articleIds, &$videoIds, &$pollIds, &$podcastIds, &$mikrowavIds) {
            $newItem = new stdClass;
            $split = explode(';', $item, 2);
            if (isset($split[0])) {
                $newItem->uniqueId = $split[0];
            }
            if (isset($split[1])) {
                //keep track_code to deliver to client
                $newItem->trackCode = $split[1];
            }

            $newItem->contentType = $this->getType($newItem->uniqueId);

            if ($newItem->contentType == "article") {
                $articleIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "video") {
                $videoIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "podcast") {
                $podcastIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "poll") {
                $pollIds[] = $newItem->uniqueId;
                $articleIds[] = $newItem->uniqueId;
            } else if ($newItem->contentType == "mikrowav") {
                $mikrowavIds[] = $newItem->uniqueId;
            }
            return $newItem;
        });

        $pollsCount = $contents->where('contentType', 'PO')->count();
        if ($pollsCount > 0) {
            $pollService = app(PollServiceImplV4::class);
            $polls = $pollService->getPollToInject($pollsCount);
            $contents = $contents->map(function ($item) use (&$polls, &$pollIds, &$articleIds, &$podcastIds) {
                if ($item->contentType == "PO") {
                    $item->contentType = "poll";
                    $item->uniqueId = array_shift($polls);
                    $pollIds[] = $item->uniqueId;
                    $articleIds[] = $item->uniqueId;
                }
                return $item;
            });
        }
        //get body contents;
        $articleContents = collect();
        $mikrowavContents = collect();
        $videoContents = collect();
        $podcastContents = collect();
        $pollContents = collect();
        $pinnedArticles =  [];
        $pinnedVideos =  [];
        $pinnedPodcasts =  [];
        if (count($articleIds) > 0) {
            $articleContents = $this->getArticles($articleIds)->keyBy('unique_id');
            $pinnedArticles = $pinFeed ? $pinService->getPinned($pinFeed, ['article', 'highlight'], $languages) : $pinnedArticles;
        }
        if (count($videoIds) > 0) {
            $videoContents = $this->getVideos($videoIds)->keyBy('unique_id');
            $pinnedVideos = $pinFeed ? $pinService->getPinned($pinFeed, ['video', 'highlight'], $languages) : $pinnedVideos;
        }
        if (count($podcastIds) > 0) {
            $podcastContents = $this->getPodcasts($podcastIds)->keyBy('unique_id');
            $pinnedPodcasts = $pinFeed ? $pinService->getPinned($pinFeed, ['podcast', 'highlight'], $languages) : $pinnedPodcasts;
        }

        if (count($pollIds) > 0) {
            $pollService = app(PollServiceImplV4::class);
            $pollContents = $pollService->getPollsByArticleIds($pollIds, Auth::user()->getFirebaseId());
        }

        if (count($mikrowavIds) > 0) {
            $mikrowavContents = Mikrowav::join('userLogin', 'mikrowavs.user_id', '=', 'userLogin.id')
                ->select('mikrowavs.*')
                ->whereIn('mikrowavs.unique_id', $mikrowavIds)
                ->whereNull('userLogin.deleted_at')
                ->get()
                ->keyBy('unique_id');
        }
        //retrieve topic and language fromc content_pool table
        $contentInfo = collect();
        $contentInfo = $contentInfo->merge($this->getContentInfo($articleIds, 'article'));
        $contentInfo = $contentInfo->merge($this->getContentInfo($videoIds, 'video'));
        $contentInfo = $contentInfo->merge($this->getContentInfo($podcastIds, 'podcast'));
        $topicRepo = app(TopicRepository::class);
        $topicsData = $topicRepo->getAllTopics();
        $mainLanguage = Auth::user()->getMainLanguage();

        //map content
        $contents = $contents->map(function ($item) use (
            &$articleContents,
            &$mikrowavContents,
            &$videoContents,
            &$pollContents,
            &$podcastContents,
            $mode,
            &$contentInfo,
            &$topicsData,
            &$mainLanguage,
            $pinnedArticles,
            $pinnedVideos,
            $pinnedPodcasts,
            &$showContent
        ) {
            if ($item->contentType === 'mikrowav') {

                $content = $mikrowavContents->first(function ($mikrowavContent) use ($item) {
                    return strtolower($mikrowavContent->unique_id) === strtolower($item->uniqueId);
                });

                if ($content === null || $content->user === null || $content->user->userProfile === null) {
                    return null;
                }
                $item = $this->decorateMikrowavContent($item, $content);
            }

            if (in_array($item->contentType, ["article", "poll"])) {
                if (!$item->uniqueId) return $item;
                $content = $articleContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->description;
                $item->isNative = (bool) $content->readerViewOnly;
                $item->publishedDate = $content->publishedDate;
                $item->hideAds = $content->hideAds;
                $item->deleted = $content->deleted ?? 0;
                $item->stats = (object)[
                    "views" => $content->viewCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->shares,
                    "reactions" =>  $content->articleReaction,
                    "userReactionType" => $content->userReactionType,
                    "adEnable" => (int) $content->adEnable,
                    "readerViewOnly" => (bool)  $content->readerViewOnly,
                    "showWebsite" => (int) $content->showWebsite,
                    "enableJS" => (int) $content->enableJS,
                    "boostReaderView" => (int)  $content->boostReaderView,
                ];

                unset($item->stats->articleID);
                $size = "1000x0";
                $quality = "q50";

                $shareImageUrl = !empty($content->mediaArray) ? (!empty($content->mediaArray[0]['url']) ? $content->mediaArray[0]['url'] : $content->mediaArray[0]['thumbnail'] ): null;

                if (Str::startsWith($shareImageUrl, 'https://imgproxy.newswav.com')) {
                    $shareImageUrl = preg_replace_callback('/(https:\/\/imgproxy\.newswav\.com\/)([0-9]*x0)/',function($m) {
                        return $m[1].'1000x0';
                    },$shareImageUrl);
                }

                $cleanUrl = cleanParam($content->canonicalURL, ["utm_source", "utm_medium"]);
                $item->meta = (object)[
                    "article" => (object)[
                        "hideAds" => $content->hideAds,
                        "pinned" => in_array($item->uniqueId, $pinnedArticles) ? 1 : 0,
                        "showOriginalArticle" => in_array(($content->publisher->project ?? ''), self::SHOW_ARTICLE_POJECTS) ? false : true,
                        "language" => $content->language,
                        "url" => (Str::contains($cleanUrl, "?")) ? $cleanUrl . "&utm_source=Newswav&utm_medium=Website" : $cleanUrl . "?utm_source=Newswav&utm_medium=Website",
                        "permalink" => $content->permalink,
                        "thumbnailUrl" => !empty($content->mediaArray) ? (!empty($content->mediaArray[0]['thumbnail']) ? $content->mediaArray[0]['thumbnail'] : $content->mediaArray[0]['url']) : null,
                        "originalUrl" => !empty($content->mediaArray) ? (!empty($content->mediaArray[0]['url']) ? $content->mediaArray[0]['url'] : $content->mediaArray[0]['thumbnail']) : null,
                        "shareImageUrl" => $shareImageUrl,
                    ],
                ];

                if ($showContent){
                    $item->meta->article->content = $this->cleanScriptTag($content->html);
                }

                if ($mode == static::CONTENT_MODE_BRIEF) {
                    unset($item->meta->article->content);
                }

                if ($mode == static::CONTENT_MODE_SUPER_BRIEF) {
                    //unset stats
                    $tempViews = $item->stats->views;
                    unset($item->stats);
                    $item->stats = (object)[
                        'views' => $tempViews
                    ];

                    unset($item->meta->article->content);
                }
            }

            if ($item->contentType == "video") {
                $content = $videoContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->description;
                $item->thumbnailUrl = isset($content->images[0]) ? $content->images[0]["url"] : null;
                $item->isNative = true; //hardcode to 1 for now?
                $item->publishedDate = Carbon::createFromTimestamp($content->published_date)->format('Y-m-d H:i:s');
                $item->deleted = $content->deleted;
                $item->hideAds = $content->hideAds;
                $item->stats = (object)[
                    "views" => (int) $content->viewsCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->sharesCount,
                    "reactions" =>  $content->reactions,
                    "userReactionType" => $content->userReactionType
                ];
                unset($item->stats->articleID);
                $item->meta = (object)[
                    "video" => (object)[
                        "hideAds" => $content->hideAds,
                        "type" => $content->type,
                        "pinned" => in_array($item->uniqueId, $pinnedVideos) ? 1 : 0,
                        "url" => $content->video_url_1 ?? $content->video_url_2,
                        "permalink" => $content->permalink,
                        "language" => $content->language,
                        "videoUrl" => $content->page_url ?? "",
                        "thumbnailUrl" => isset($content->images[0]) ? $content->images[0]["url"] : null,
                        "duration" => $content->video_duration,
                        "aspectRatio" => $content->aspect_ratio,
                        "mimeType" => $content->mime_type,
                        "playerVideoSrc" => $content->video_url_1 ?? $content->video_url_2,
                        "isLive" => $content->is_live == 1 ? true : false
                    ],
                ];

                if ($showContent){
                    $item->meta->video->content = $this->cleanScriptTag($content->content);
                }
            }
            if ($item->contentType == "podcast") {
                $content = $podcastContents->get($item->uniqueId);
                if (!$content) {
                    $item->uniqueId = null;
                    return $item;
                }
                $item->title = $content->title;
                $item->description = $content->description;
                $item->thumbnailUrl = isset($content->images[0]) ? $content->images[0]->url : null;
                $item->isNative = true; //hardcode to 1 for now?
                $item->publishedDate = Carbon::createFromTimestamp($content->published_date)->format('Y-m-d H:i:s');
                $item->deleted = $content->deleted;
                $item->hideAds = $content->hideAds;
                $item->stats = (object)[
                    "views" => (int) $content->viewsCount,
                    "comments" => (int) $content->commentCount,
                    "shares" => (int) $content->sharesCount,
                    "reactions" =>  $content->reactions,
                    "userReactionType" => $content->userReactionType
                ];
                $cleanUrl = cleanParam($content->url, ["utm_source", "utm_medium"]);
                unset($item->stats->articleID);
                $item->meta = (object)[
                    "podcast" => (object)[
                        "hideAds" => $content->hideAds,
                        "pinned" => in_array($item->uniqueId, $pinnedPodcasts) ? 1 : 0,
                        "language" => $content->language,
                        "url" => $content->pod_url,
                        "showName" => $content->show_name,
                        "permalink" => $content->permalink,
                        "podcastUrl" => (Str::contains($cleanUrl, "?")) ? $cleanUrl . "&utm_source=Newswav&utm_medium=Website" : $cleanUrl . "?utm_source=Newswav&utm_medium=Website",
                        "thumbnailUrl" => isset($content->images[0]) ? $content->images[0]->url : null,
                        "duration" => $content->pod_duration,
                    ],
                ];

                if ($showContent){
                    $item->meta->podcast->content = $this->cleanScriptTag($content->content ?? "");
                }
            }
            if (in_array($item->contentType, ['article', 'video', 'podcast', 'poll'])){
                switch ($item->contentType){
                    case 'article':
                        $pinnedIds = $pinnedArticles;
                        break;
                    case 'video':
                        $pinnedIds = $pinnedVideos;
                        break;
                    case 'podcasts':
                        $pinnedIds = $pinnedVideos;
                    default:
                        $pinnedIds = [];
                }
                $item->publisher = (object)[
                    "id" => $content->publisher->id,
                    "name" => $content->publisher->publisherName,
                    "logo" => $content->publisher->publisherLogoURL,
                    "isVerified" => (bool) $content->publisher->verified,
                    "isFollowing" => (bool) $content->publisher->is_following,
                    "project" => $content->publisher->project,
                    'enabled' => $content->publisher->enabled,
                    'websiteUrl' => $content->publisher->websiteUrl,
                    'permalink' => $content->publisher->permalink,
                    'isNewswav' => in_array($content->publisher->id, nw_bunker('publisher', 'newswav_publishers', [])),
                    'showFollowButton' => !in_array($content->publisher->id, nw_bunker('publisher', 'hide_follow_button', [])),
                    'showHideButton' => in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_hide_button', [])) ? true : false),
                ];
                $item->showHideButton = in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_content_hide_button', [])) ? true : false);
                $item->showNotInterestedButton = in_array($item->uniqueId, $pinnedIds) ? false : (!in_array($content->publisher->id, nw_bunker('publisher', 'hide_not_interested_button', [])) ? true : false);
                unset($pinnedIds);
            }
            //assign topic and language to meta
            if (in_array($item->contentType, ['article', 'video', 'podcast'])) {
                $currentContent = $contentInfo->get($item->uniqueId);
                $item->meta->{$item->contentType}->language = $currentContent ? $currentContent->max('language') : null;
                if ($item->contentType == 'podcast'){
                    $currentTopic = $currentContent ? $currentContent->pluck('topic')->toArray() : [];
                    $topics = $topicsData->whereIn('id', array_map(function ($item){
                        if ($item == 18){
                            return 1;
                        }
                        return $item;
                    }, $currentTopic));
                }else{
                    $topics = $topicsData->whereIn('id', $currentContent ? $currentContent->pluck('topic') : null);
                }
                if ($topics) {
                    $topicArray = [];
                    foreach ($topics as $topic) {
                        $new = new stdClass;
                        $new->id = $topic->id;
                        $new->en = $topic->nameEN;
                        $new->ms = $topic->nameMS;
                        $new->zh = $topic->nameZH;
                        $new->subscribed = (int)$topic->is_following;
                        $topicArray[] = $new;
                    }
                    $item->meta->{$item->contentType}->topics = $topicArray;
                }
            }

            if ($item->contentType == "poll") {
                $poll = $pollContents->get($item->uniqueId)->first();
                $item->title = $poll->question;
                $item->meta->poll = $poll;
                $item->description = implode("; ", collect($poll->options)->pluck('text')->toArray());
                unset($item->meta->article);
            }


            return $item;
        });

        return $contents->filter(function ($item) {
            return $item && $item->uniqueId;
        })->values();
    }

    public function getPublisherBody($pubIds){
        $publishers = DB::table('publishers')->whereIn('id', $pubIds)->get()->map(function($item){
            $new = (object)[
                "id" =>  $item->id,
                "contentType" => "publisher",
                "name" => $item->name,
                "description" => $item->description,
                "logoUrl" => $item->logo_url,
                "isVerified" => $item->verified,
                "totalFollowers" => $item->followers,
                "project" => $item->project,
                "websiteUrl" => $item->website_url,
                "permalink" => $item->permalink,
            ];

            return $new;
        });

        // rearrange based on order of $pubIds fed to method
        $sortedPublishers = $publishers->sortBy(function ($publisher) use ($pubIds) {
            return array_search($publisher->id, $pubIds);
        })->values();

        return $sortedPublishers;

    }

    public function getContentSuggestions($languages, $page = 1, $limit = 3, $type = 'a')
    {
        $hours = [
            'a' => nw_bunker('query', 'article_suggestion_hour', 8),
            'v' => nw_bunker('query', 'video_suggestion_hour', 15),
            'p' => nw_bunker('query', 'podcast_suggestion_hour', 96),
        ];

        $user = Auth::user();
        $firebaseId = $user->getFirebaseId();

        $reportRepo = app(ReportRepository::class);
        $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
        $excludePubIds = $user->getMutedPublishers();
        $disabledChannels = getDisabledChannelsFromCache(true);
        $key = "{$firebaseId}_{$type}_suggestions";
        $excludeSlugs = [];
        if ($page > 1){
            $excludeSlugs = cache_get($key, []);
        }

        $q = DB::table('content_pool')
            ->leftJoin('channels','content_pool.channel','=','channels.id')
            ->leftJoin('disabled_contents', 'disabled_contents.unique_id','=','content_pool.unique_id')

            ->where('content_pool.created_at', '<=', Carbon::now())
            ->whereIn('content_pool.type', [$type])
            ->where('content_pool.published_date', '<=', Carbon::now()->timestamp)
            ->where('content_pool.published_date', '>=', Carbon::now()->subHours($hours[$type])->timestamp)
            ->whereNotIn('content_pool.similarity_group_slug', $excludeSlugs)
            ->whereNotIn('content_pool.unique_id', nw_bunker('article', 'covid_all', []))
            ->where('channels.listing_enabled',1)
            ->whereIn('content_pool.language', $languages)
            ->whereNull('disabled_contents.unique_id')
            ->when($firebaseId, function ($query) use ($firebaseId){
                return $this->excludeNotInterestedQuery($query, 'content_pool.unique_id', $firebaseId);
            });
            if($reportedArticles && count($reportedArticles)){
                $q = $q->whereNotIn('content_pool.unique_id', $reportedArticles);
            }
            if ($disabledChannels && count($disabledChannels) > 0){
                $q = $q->whereNotIn('content_pool.channel', $disabledChannels);
            }
            //clone query and make as subquery
            $innerQuery = clone $q;
            $innerQuery = getEloquentSqlWithBindings($innerQuery->selectRaw(DB::raw("similarity_group_slug, MAX( score ) AS score"))->groupBy('similarity_group_slug'));
        $q->select('content_pool.unique_id', 'content_pool.similarity_group_slug', 'content_pool.score')
            ->join(DB::raw("({$innerQuery}) as b"),
            function($join)
            {
                $join->on('content_pool.similarity_group_slug', '=', 'b.similarity_group_slug')
                    ->whereRaw(DB::raw('content_pool.score = b.score'));
            })
            ->groupBy('similarity_group_slug')
            ->orderBy('content_pool.score', 'desc')
            ->orderBy('content_pool.updated_at');

        $content = $q->limit($limit)->get();
        $serve = $content->pluck('similarity_group_slug')->toArray();
        $exclude = array_merge($excludeSlugs, $serve);
        cache_put($key, $exclude, 10);
        return $content->pluck('unique_id')->toArray();

    }

    public function getPublisherSuggestion($languages, $page, $limit){
        //randomize every 15 minutes
        $seed = cache('publisher_suggestion_seed', function(){
            return time();
        }, 15);

        $disabled = getDisabledChannelsFromCache();

        $data = DB::table('channels')
            ->join('publishers', 'channels.publisher_id', '=', 'publishers.id')
            ->select('publisher_id', DB::raw('min(channels.reader_view_only) as isNative'))
            ->distinct()
            ->where(function($query){
                $query->where('has_articles', 1)
                ->orWhere('has_videos', 1)
                ->orWhere('has_podcasts', 1);
            })
            ->where('publishers.enabled', 1)
            ->whereNotIn('channels.id', explode(",", $disabled))
            ->whereIn('channels.language', $languages)
            ->groupBy('publisher_id')
            ->havingRaw('isNative = ?', [1])
            ->inRandomOrder("$seed")
            ->simplePaginate($limit);

        return $data->getCollection()->pluck('publisher_id')->toArray();
    }

    public function getTopicSuggestion($page, $limit, $excludeTopics){
        $topicRepository = app(TopicRepository::class);
        $topics = $topicRepository->getNotFollowingTopics($excludeTopics);
        return $topics->pluck('id')->toArray();
    }

    private function getContentInfo($contentIds, $type)
    {
        //get topics and article from predictions table
        if ($type == "article") {
            return DB::table('predictions')->select('unique_id', 'topic', 'language')->whereIn('unique_id', $contentIds)->get()->groupBy('unique_id');
        }

        if ($type == "video") {
            return DB::table('video_predictions')->select('unique_id', 'topic', 'language')->whereIn('unique_id', $contentIds)->get()->groupBy('unique_id');
        }

        if ($type == "podcast") {
            return DB::table('podcast_prediction')->select('podcast_unique_id as unique_id', 'topic', 'language')->whereIn('podcast_unique_id', $contentIds)->get()->groupBy('unique_id');
        }
    }

    public function getRelatedArticles($uniqueId, $suggestLimit): array
    {
        $relatedArticles = DB::table('article')
            ->where('uniqueID', $uniqueId)
            ->when($suggestLimit, function ($query) use ($suggestLimit) {
                return $query->limit($suggestLimit);
            })
            ->first(['relatedArticle']);

        if (!$relatedArticles || $relatedArticles->relatedArticle == "") {
            return [];
        }
        $contentIds = explode(',', $relatedArticles->relatedArticle);

        $disabledContents = DB::table('disabled_contents')->whereIn('unique_id',$contentIds)->get()->pluck('unique_id')->toArray();

        // to exclude the disabled content ids from the array
        $enabledContentIds = array_values(array_diff($contentIds, $disabledContents));

        return $enabledContentIds;
    }

    public function getVideoPredictionQuery($uniqueId, $columns = ['*']){
        return DB::table('video_predictions')
            ->select($columns)
            ->where('unique_id', $uniqueId);
    }

    /**
     * Changing the structure for this function and functions related to it
     * to standardize repo to hold query only
     */
    public function getRelatedVideosQuery($uniqueId, $suggestLimit = 10, $languages, $topics): \Illuminate\Database\Query\Builder
    {
        return DB::table('video_predictions')->whereIn('topic', $topics,)
                            ->select('unique_id')
                            ->when($suggestLimit, function ($query) use ($suggestLimit) {
                                return $query->limit($suggestLimit);
                            })
                            ->where('published_date', '<=',Carbon::now()->timestamp)
                            ->whereNotIn('unique_id', [$uniqueId])
                            ->whereIn('language', $languages)
                            ->orderByDesc('published_date');

    }

    public function getRelatedVideosQueryWithoutLimit($uniqueId, $languages, $topics, $lastPublishedDate = null): \Illuminate\Database\Query\Builder
    {
        $query = DB::table('video_predictions')
        ->whereIn('topic', $topics)
        ->select('unique_id')
        ->where('published_date', '<=', Carbon::now()->timestamp)
        ->whereNotIn('unique_id', [$uniqueId])
        ->whereIn('language', $languages)
        ->orderByDesc('published_date');

        if ($lastPublishedDate) {
            $query->where('published_date', '<', $lastPublishedDate);
        }

        return $query;

    }

    private function getUserArticlesData($profileId, $articlesIds)
    {
        $postRepo = app(PostRepository::class);
        $userReactions = $postRepo->getUserReactionsOnArticleEntities($articlesIds, $profileId);
        return ["reactions" => $userReactions];
    }

    private function getArticles($ids): Collection
    {
        $cdnUrl = nw_bunker('gcloud', 'cdn_newswav_url', 'https://cdn.newswav.com/');
        $hostUrl = nw_bunker('host', 'https', 'https://newswav.com/');

        $query = DB::table('article')->leftJoin('category', 'category.id', '=', 'article.categoryID')
            ->leftJoin('channels', 'channels.id', '=', "article.channelID")
            ->leftJoin('hide_ads_contents', 'hide_ads_contents.unique_id', '=', "article.uniqueID")
            ->leftJoin('predictions', 'predictions.unique_id', '=', 'article.uniqueID')
            ->whereIn('uniqueID', $ids)
            ->select(
                'article.id',
                'article.uniqueID as unique_id',
                'article.articleID',
                'article.updatedDate',
                'article.publishedDate',
                'article.channelID',
                'article.url',
                'article.canonicalURL',
                'article.title',
                'article.author',
                'article.description',
                'article.tagsName',
                'article.html',
                'article.isOnline',
                'article.isFixed',
                'article.permalink',
                'predictions.topic',
                'category.name as categoryName',
                "channels.name as channelName",
                "channels.image_url as channelImageURL",
                "channels.image_night_url as channelImageNightURL",
                "channels.language",
                "channels.ad_enabled as adEnable",
                "channels.reader_view_only as readerViewOnly",
                "channels.show_website as showWebsite",
                "channels.js_enabled as enableJS",
                "channels.boost_reader_view as boostReaderView",
                "hide_ads_contents.unique_id as hideAds",
                "hide_ads_contents.hidden_at as adsHiddenAt"
            );

        $articles = $query->get();
        $categories = array();
        $articleUniqueIds = array();
        $publishers = array();
        $reactions = array();

        $articles->each(function ($item, $key) use (&$articleUniqueIds, &$tags, &$publishers, $cdnUrl, $hostUrl) {


            if (in_array($item->unique_id, nw_bunker('article', 'covid'))) {
                $item->url = $item->url . "&xy" . rand(0, 999) . "=" . rand(0, 999) . "&z=" . rand(0, 99);
            }

           $articleUniqueIds[] = $item->unique_id;

            if ($item->channelID &&  $item->channelID > 0) {
                $publishers[] = $item->channelID;
            }
        });

        $thumbnails = DB::table('article_thumbnail')
        ->whereIn('articleUniqueId', $articleUniqueIds)
        ->orderByDesc('updatedAt')
        ->get()
        ->unique('articleUniqueId')
        ->keyBy('articleUniqueId');
        $publishersData = false;
        $deletedData = DB::table('predictions')
            ->select(DB::raw("unique_id, IF(language = 'DEL', 1, 0) as deleted"))
            ->whereIn('unique_id', $ids)
            ->get()
            ->keyBy('unique_id');

        $disabledCommentsData = DB::table('disabled_comments')
            ->whereIn('unique_ID', $ids)
            ->whereNull('deleted_at')
            ->get()
            ->keyBy('unique_ID');

        $boostData = getBoostData()->keyBy('unique_id');

        if (count($publishers) > 0) {
            $publisherRepo = app(PublisherRepository::class);
            $publishersData =  $publisherRepo->getPublishersByChannelIds($publishers);
        }

        $reactionData = DB::table('articleReaction')
            ->select('articleID', "happy", "cry", "shocked", "laugh", "meh", "angry")
            ->whereIn('articleID', $ids)
            ->get()
            ->keyBy('articleID');

        $statsData = DB::table('article_stats')
            ->whereIn('article_id', $ids)
            ->select(
                'article_id',
                'shares as shares',
                'reactions as reactionCount',
                'comments as commentCount',
                'views as viewCount',
            )
            ->get()
            ->keyBy('article_id');

        $profileId = Auth::user()->getProfileId();
        $userArticlesData = $this->getUserArticlesData($profileId, $ids);
        $articles = $articles
            ->map(function ($item) use (
                $thumbnails,
                $reactionData,
                $publishersData,
                $boostData,
                $statsData,
                $deletedData,
                $disabledCommentsData,
                $hostUrl,
                $cdnUrl,
                $userArticlesData
            ) {
                $item->mediaArray = array();
                $item->recommendedArticleArray = array();

                unset($item->keywordTitle);
                unset($item->keywordDescription);
                unset($item->keywordHTML);

                //$item->html = str_replace('="//', '="http://', $item->html);

                $item->deleted =  $deletedData->get($item->unique_id)->deleted ?? 0;
                $item->hideAds = ($item->hideAds && $item->adsHiddenAt) ? true : false;
                $item->comments_disabled = $disabledCommentsData->get($item->unique_id) ? 1 : 0;

                $thumb = $thumbnails->get($item->unique_id);

                if ($thumb) {
                    $item->mediaArray[] = [
                        'url' => $thumb->originalMediaUrl,
                        'thumbnail' => $thumb->wideUrl
                            ?: $thumb->squareUrl
                            ?: $thumb->originalMediaUrl,
                        'wideUrl'   => $thumb->wideUrl,
                        'squareUrl' => $thumb->squareUrl,
                        'caption'   => $thumb->caption,
                        'width'     => $thumb->wideImageWidth,
                        'height'    => $thumb->wideImageHeight,
                    ];
                }

                $stats = $statsData->get($item->unique_id);
                if (!$stats) {
                    $item->commentCount = 0;
                    $item->viewCount = 1;
                    $item->shares = 0;
                } else {
                    $item->commentCount = $stats->commentCount ? $stats->commentCount : 0;
                    $item->viewCount = $stats->viewCount &&  $stats->viewCount > 0 ? $stats->viewCount : 1;
                    $item->shares = $stats->shares ? $stats->shares : 0;
                }

                $item->userReactionType = $userArticlesData["reactions"]->get($item->unique_id)->reaction_type ?? 0;
                $item->articleReaction = $reactionData->get($item->unique_id);
                if (!$item->articleReaction) {
                    $articleReaction = (object) [
                        'happy' => 0,
                        'cry' => 0,
                        'shocked' => 0,
                        'meh' => 0,
                        'angry' => 0,
                        'laugh' => 0
                    ];

                    $item->articleReaction = $articleReaction;
                    $item->reactionCount = 0;
                } else {
                    $item->reactionCount = (int)$item->articleReaction->happy +
                        (int)$item->articleReaction->cry +
                        (int)$item->articleReaction->shocked +
                        (int)$item->articleReaction->angry +
                        (int)$item->articleReaction->laugh +
                        (int)$item->articleReaction->meh;

                    unset($item->articleReaction->articleID);
                }

                if ($item->channelID) {
                    $chId = $item->channelID;
                    $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                        return in_array($chId, $value->channels);
                    });
                    if (!$item->publisher) {
                        // log_emergency('Publisher not found ! for channel ' . $chId);
                        //remove to reduce bugsnag error
                        //$item->publisher->canOpen = 0;
                    } else {
                        $item->publisher->canOpen =  $item->publisher->id == 369 ? 0  :  1;
                    }
                }
                $dataToShow = collect([
                    ['name' => 'showReactions', 'count' => 5 * $item->reactionCount],
                    ['name' => 'showComments', 'count' => 10 * $item->commentCount],
                    ['name' => 'showViews', 'count' => $item->viewCount]
                ])->sortByDesc('count')
                    ->whereNotIn('count', [0])
                    ->slice(0, 3)
                    ->keyBy('name');

                if (Str::startsWith($item->url, "https://capital-stock.com/")) {
                    $item->url = $hostUrl . $item->unique_id;
                }

                if (Str::startsWith($item->canonicalURL, "https://capital-stock.com/")) {
                    $item->canonicalURL = $hostUrl . $item->unique_id;
                }

                if ($boostData->get($item->unique_id)) {
                    $item->articleReaction->happy = $item->articleReaction->happy  + $boostData->get($item->unique_id)->happy;
                    $item->articleReaction->shocked = $item->articleReaction->shocked  + $boostData->get($item->unique_id)->shocked;
                    $item->articleReaction->laugh = $item->articleReaction->laugh  + $boostData->get($item->unique_id)->laugh;
                    $item->viewCount = $item->viewCount  + $boostData->get($item->unique_id)->views;
                    $item->reactionCount = $item->reactionCount  + $boostData->get($item->unique_id)->laugh + $boostData->get($item->unique_id)->shocked + $boostData->get($item->unique_id)->happy;
                }

                $articleIDSabahElection = nw_bunker('article', 'sabah_election_2020');

                if (in_array($item->unique_id, $articleIDSabahElection)) {
                    $item->url = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=" . rand(0, 999) . "&x=" . rand(0, 9999);
                    $item->canonicalURL = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=" . rand(0, 999) . "&x=" . rand(0, 9990);
                }

                return $item;
            });
        return $articles->map(function ($article) {
            return replaceMediaInOldArticle($article);
        });
    }

    private function getVideos($ids): Collection
    {
        $query = DB::table('videos')
            ->select(
                'videos.id',
                'videos.title',
                'videos.description',
                'videos.content',
                'videos.published_date',
                'videos.aspect_ratio',
                'videos.unique_id',
                'videos.video_url_2',
                'videos.video_url_1',
                'videos.thumb_url',
                'videos.video_duration',
                'videos.page_url',
                'videos.channel_id',
                'videos.category_id',
                'videos.publisher_id',
                'videos.mime_type',
                'videos.deleted_at',
                'videos.fallback_thumb_url',
                'videos.permalink',
                'channels.language',
                'videos.player_type',
                'videos.type',
                'videos.youtube_link',
                'videos.is_live',
                'videos.long_desc',
                "hide_ads_contents.unique_id as hideAds",
                "hide_ads_contents.hidden_at as adsHiddenAt",
                'videos.first_frame_thumb_url'
            )
            ->join('channels', 'channels.id' , '=', 'videos.channel_id')
            ->leftJoin('hide_ads_contents', 'hide_ads_contents.unique_id', '=', "videos.unique_id")
            ->where('published', 1)
            ->whereIn('videos.unique_id', $ids);
        $idsString = "";
        foreach ($ids as $id) {
            $idsString = $idsString . "'$id',";
        }
        $idsString = $idsString . "'0'";
        $query->orderByRaw("FIELD (videos.unique_id, $idsString)");

        $videos = $query->get();
        if (
            $videos &&
            !$videos->isEmpty()
        ) {
            $channelIds = [];
            $categorIds = [];
            $videoUniqueIds = [];
            $videos->each(function ($item, $key) use (
                &$channelIds,
                &$categorIds,
                &$videoUniqueIds
            ) {

                $videoUniqueIds[] = $item->unique_id;
                if (!empty($item->categories)) {
                    foreach (explode(',', $item->categories) as $cat) {
                        $categorIds[$cat] = [$cat];
                    }
                }

                if ($item->channel_id) {
                    $channelIds[] = $item->channel_id;
                }
            });
            $publishersData = collect();
            if (count($channelIds) > 0) {
                $publisherRepo = app(PublisherRepository::class);
                $publishersData =  $publisherRepo->getPublishersByChannelIds($channelIds);
            }
            $categories = DB::table('video_categories')
                ->select('name', 'id')
                ->whereIn('id', array_keys($categorIds))
                ->get()
                ->keyBy('id');

            $stats = DB::table('video_stats')
                ->whereIn('video_id', $videoUniqueIds)
                ->get()
                ->keyBy('video_id');


            $boostData = getBoostData()->keyBy('unique_id');

            $disabledCommentsData = $this->getDisabledCommentsData($ids);
            $firebase_id = Auth::user()->getFirebaseId();
            $profileId = Auth::user()->getProfileId();
            $postRepo = app(PostRepository::class);
            $userReactions = $postRepo->getUserReactionsOnVideoEntities($ids, $profileId);
            $reactions = $postRepo->getReactionsOnVideoEntities($ids);

            // temporary filter out youtube video because of error code 15, pending app developer to check on it
            $videos = $videos->filter(function ($item) {
                return $item->player_type !== 'youtube';
            });

            $videos->transform(function ($item, $key) use (&$channels, &$categories, &$reactions, &$userReactions, &$stats, &$boostData, &$publishersData, &$disabledCommentsData) {


                if (Str::startsWith($item->title, 'SCMP') || Str::startsWith($item->title, 'CGTN')) {
                    $item->title = substr($item->description, 0, 150) . '...';
                }

                if ($item->channel_id) {
                    $chId = $item->channel_id;
                    $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                        return in_array($chId, $value->channels);
                    });
                    if (!$item->publisher) {
                        // log_emergency('Publisher not found ! for channel ' . $chId);
                        // remove to reduce bugsnag error
                        //$item->publisher->canOpen = 0;
                    } else {
                        $item->publisher->canOpen =  $item->publisher->id == 369 ? 0  :  1;
                    }
                }
                if ($item->category_id) {
                    $item->category = $categories->get($item->category_id) ?? "";
                }

                $videos = [];
                $item->images = array(['url' => !empty($item->thumb_url) ? $item->thumb_url : ($item->first_frame_thumb_url ?? "")]);
                $item->kind = "video";
                $item->id = $item->id;
                $item->reactions = $reactions->get($item->unique_id) ?? [
                    "happy" => 0,
                    "cry" => 0,
                    "shocked" => 0,
                    "laugh" => 0,
                    "meh" => 0,
                    "angry" => 0
                ];
                $item->reactionsCount =  $stats->get($item->unique_id)->reactions ?? 0;
                $item->viewsCount = ($stats->get($item->unique_id)->play90 ?? 1)
                    + ($stats->get($item->unique_id)->play50 ?? 1)
                    + ($stats->get($item->unique_id)->autoplay ?? 1);
                $item->viewsCount  = $item->viewsCount > 0 ? $item->viewsCount : 1;
                $item->sharesCount = $stats->get($item->unique_id)->shares ?? 0;
                $item->commentCount = $stats->get($item->unique_id)->comments ?? 0;

                $dataToShow = collect([
                    ['name' => 'showReactions', 'count' => 5 * $item->reactionsCount],
                    //  ['name' => 'showShares', 'count' => 10 * $item->sharesCount],
                    ['name' => 'showViews', 'count' => $item->viewsCount]
                ])->sortByDesc('count')
                    ->whereNotIn('count', [0])
                    ->slice(0, 3)
                    ->keyBy('name');

                $item->deleted = $item->deleted_at ? 1 : 0;
                $item->hideAds = $item->hideAds && $item->adsHiddenAt ? true : false;
                $item->comments_disabled = $disabledCommentsData->get($item->unique_id) ? 1 : 0;
                unset($item->deleted_at);
                $item->userReactionType = (int) ($userReactions->get($item->unique_id)->reaction_type ?? 0);
                $item->design = [
                    "type" => 1,
                    "compact" => 0,
                    "version" => 1,
                    "showReactions" => $dataToShow->get('showReactions') ? 1 : 0,
                    "showViews" => $dataToShow->get('showViews') ? 1 : 0,
                    "showShares" => $dataToShow->get('showShares') ? 1 : 0,
                    "showComments" => 0, //$dataToShow->get('showComments') ? 1 : 0
                ];

                if ($boostData->get($item->unique_id)) {
                    $item->reactions->happy = $item->reactions->happy  + $boostData->get($item->unique_id)->happy;
                    $item->reactions->shocked = $item->reactions->shocked  + $boostData->get($item->unique_id)->shocked;
                    $item->reactions->laugh = $item->reactions->laugh  + $boostData->get($item->unique_id)->laugh;
                    $item->viewsCount = $item->viewsCount  + $boostData->get($item->unique_id)->views;
                    $item->reactionsCount = $item->reactionsCount + ($boostData->get($item->unique_id)->happy + $boostData->get($item->unique_id)->shocked + $boostData->get($item->unique_id)->laugh);
                }
                unset($item->thumb_url);
                unset($item->publisher_id);
                unset($item->channel_id);
                unset($item->category_id);
                return $item;
            });
        }
        return $videos;
    }

    private function getPodcasts($ids): Collection
    {
        {
            $query = DB::table('podcasts')
            ->select(
                'podcasts.id',
                'podcasts.title',
                'podcasts.description',
                'podcasts.content',
                'podcasts.published_date',
                'podcasts.show_name',
                'podcasts.unique_id',
                'podcasts.author',
                'podcasts.pod_size',
                'podcasts.pod_duration',
                'podcasts.content',
                'podcasts.media',
                'podcasts.categories',
                'podcasts.pod_url',
                'podcasts.url',
                'podcasts.channel_id',
                'podcasts.category_id',
                'podcasts.deleted_at',
                'podcasts.permalink',
                'channels.language',
                'podcasts.long_desc',
                'podcasts.deleted_at',
                "hide_ads_contents.unique_id as hideAds",
                "hide_ads_contents.hidden_at as adsHiddenAt"
            )
            ->join('channels', 'channels.id' , '=', 'podcasts.channel_id')
            ->leftJoin('hide_ads_contents', 'hide_ads_contents.unique_id', '=', "podcasts.unique_id")
            ->whereIn('podcasts.unique_id', $ids);
        $idsString = "";
        foreach ($ids as $id) {
            $idsString = $idsString . "'$id',";
        }
        $idsString = $idsString . "'0'";
        $query->orderByRaw("FIELD (podcasts.unique_id, $idsString)");

        $podcasts = $query->get();
        if (
            $podcasts &&
            !$podcasts->isEmpty()
        ) {
            $channelsIds = [];
            $categorIds = [];
            $topicsIds = [];
            $mediaIds = [];
            $podcastUniqueIds = [];
            $podcasts->each(function ($item, $key) use (
                &$channelsIds,
                &$categorIds,
                &$topicsIds,
                &$podcastUniqueIds,
                &$mediaIds
            ) {

                $podcastUniqueIds[] = $item->unique_id;
                if (!empty($item->categories)) {
                    foreach (explode(',', $item->categories) as $cat) {
                        $categorIds[$cat] = [$cat];
                    }
                }

                if ($item->channel_id) {
                    $channelsIds[$item->channel_id] = [$item->channel_id];
                }

                if ($item->media) {
                    foreach(explode( ',', $item->media) as $m){
                        $mediaIds[] = $m;
                    }
                }
            });

                $publishersData = collect();
                if (count($channelsIds) > 0) {
                    $publisherRepo = app(PublisherRepository::class);
                    $publishersData =  $publisherRepo->getPublishersByChannelIds($channelsIds);
                }
                $categories = DB::table('podcast_categories')
                    ->select('name', 'id')
                    ->whereIn('id', array_keys($categorIds))
                    ->get()
                    ->keyBy('id');

                $stats = DB::table('podcast_stats')
                    ->whereIn('podcast_id', $podcastUniqueIds)
                    ->get()
                    ->keyBy('podcast_id');

                $boostData = getBoostData()->keyBy('unique_id');
                $media = DB::table('podcast_media')
                    ->select('id', 'url')
                    ->whereIn('id', $mediaIds)
                    ->get()
                    ->keyBy('id');
                $disabledCommentsData = $this->getDisabledCommentsData($ids);
                $firebase_id = Auth::user()->getFirebaseId();
                $profileId = Auth::user()->getProfileId();
                $postRepo = app(PostRepository::class);
                $userReactions = $postRepo->getUserReactionsOnPodcastEntities($ids, $profileId);
                $reactions = $postRepo->getReactionsOnPodcastEntities($ids);

                $podcasts->transform(function ($item, $key) use (&$channels, &$categories, &$reactions, &$userReactions, &$stats, &$boostData, &$media, &$publishersData, &$disabledCommentsData) {


                    if (Str::startsWith($item->title, 'SCMP') || Str::startsWith($item->title, 'CGTN')) {
                        $item->title = substr($item->description, 0, 150) . '...';
                    }

                    if ($item->channel_id) {
                        $chId = $item->channel_id;
                        $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                            return in_array($chId, $value->channels);
                        });
                        if (!$item->publisher) {
                            // log_emergency('Publisher not found ! for channel ' . $chId);
                            //remove to reduce bugsnag error
                            //$item->publisher->canOpen = 0;
                        } else {
                            $item->publisher->canOpen =  $item->publisher->id == 369 ? 0  :  1;
                        }
                    }
                    if ($item->category_id) {
                        $item->category = $categories->get($item->category_id) ?? "";
                    }

                    $podcasts = [];
                    if ($item->media) {
                        foreach(explode( ',', $item->media) as $m){
                            if( $media->get($m)){
                                $item->images[] = $media->get($m);
                            }
                        }
                    }
                    $item->kind = "podcast";
                    $item->id = $item->id;
                    $item->reactions = $reactions->get($item->unique_id) ?? [
                        "happy" => 0,
                        "cry" => 0,
                        "shocked" => 0,
                        "laugh" => 0,
                        "meh" => 0,
                        "angry" => 0
                    ];
                    $item->reactionsCount =  $stats->get($item->unique_id)->reactions ?? 0;
                    $item->viewsCount = ($stats->get($item->unique_id)->p90 ?? 1)
                        + ($stats->get($item->unique_id)->p50 ?? 1)
                        + ($stats->get($item->unique_id)->plays ?? 1);
                    $item->viewsCount  = $item->viewsCount > 0 ? $item->viewsCount : 1;
                    $item->sharesCount = $stats->get($item->unique_id)->shares ?? 0;
                    $item->commentCount = $stats->get($item->unique_id)->comments ?? 0;

                    $dataToShow = collect([
                        ['name' => 'showReactions', 'count' => 5 * $item->reactionsCount],
                        //  ['name' => 'showShares', 'count' => 10 * $item->sharesCount],
                        ['name' => 'showViews', 'count' => $item->viewsCount]
                    ])->sortByDesc('count')
                        ->whereNotIn('count', [0])
                        ->slice(0, 3)
                        ->keyBy('name');

                    $item->deleted = $item->deleted_at ? 1 : 0;
                    $item->hideAds = $item->hideAds && $item->adsHiddenAt ? true : false;
                    $item->comments_disabled = $disabledCommentsData->get($item->unique_id) ? 1 : 0;
                    unset($item->deleted_at);
                    $item->userReactionType = (int) ($userReactions->get($item->unique_id)->reaction_type ?? 0);
                    $item->design = [
                        "type" => 1,
                        "compact" => 0,
                        "version" => 1,
                        "showReactions" => $dataToShow->get('showReactions') ? 1 : 0,
                        "showViews" => $dataToShow->get('showViews') ? 1 : 0,
                        "showShares" => $dataToShow->get('showShares') ? 1 : 0,
                        "showComments" => 0, //$dataToShow->get('showComments') ? 1 : 0
                    ];

                    if ($boostData->get($item->unique_id)) {
                        $item->reactions->happy = $item->reactions->happy  + $boostData->get($item->unique_id)->happy;
                        $item->reactions->shocked = $item->reactions->shocked  + $boostData->get($item->unique_id)->shocked;
                        $item->reactions->laugh = $item->reactions->laugh  + $boostData->get($item->unique_id)->laugh;
                        $item->viewsCount = $item->viewsCount  + $boostData->get($item->unique_id)->views;
                        $item->reactionsCount = $item->reactionsCount + ($boostData->get($item->unique_id)->happy + $boostData->get($item->unique_id)->shocked + $boostData->get($item->unique_id)->laugh);
                    }
                    unset($item->thumb_url);
                    unset($item->publisher_id);
                    unset($item->channel_id);
                    unset($item->category_id);
                    return $item;
                });
            }
            return $podcasts;
        }
    }

    public function decorateMikrowavContent($item, Mikrowav $content) {
        $userProfile = $content->user->userProfile;
        $item->id = $content->id;
        $item->user_id = $content->id;
        $item->content = $content->content;
        $item->totalViews = $content->total_views;
        $item->totalComments = $content->total_comments;
        $item->totalReposts = $content->total_reposts;
        $item->totalShares = $content->total_shares;
        $item->isShadowBanned = $content->is_shadow_banned;
        $item->isFlagged = $content->is_flagged;
        $item->publishedDate = Carbon::parse($content->created_at)->toDateTimeString();
        $item->user = (object)[
            "username"=> $userProfile->username,
            "email"=> $userProfile->email,
            "name" =>$userProfile->name,
            "mobileNumber"=> $userProfile->mobile_number,
            "profilePicture"=> $userProfile->profile_picture,
            "birthDate"=> $userProfile->birth_date,
            "gender"=> $userProfile->gender,
            "isPrivate"=> $userProfile->is_private,
            "bio"  =>$userProfile->bio,
            "location"=> $userProfile->location,
            "website"=> $userProfile->website,
            "qualification"=> $userProfile->qualification,
            "firebaseId"=> $content->user->loginProviderUID
        ];
        $item->media = $content->media->map(function ($media) {
            return (object)[
                "caption" => $media->caption,
                "url" => $media->url,
                "type" => $media->type,
                "aspect_ratio" => $media->image_width > 0 && $media->image_height > 0 ?
                    calculateAspectRatio($media->image_width, $media->image_height) : ''
            ];
        })->toArray();
        $item->meta = (object)[
            "mikrowav" => (object)[
                "thumbnailUrl" => $content->media->count() > 0 ? $content->media->first()->url : null
            ]
        ];
        return $item;
    }

    public function getType($id): String
    {

        if (like_match('AD', $id) || like_match('PO', $id)) return $id;

        if (like_match('A%_P_%', $id)) {
            return "poll";
        }
        if (like_match('A%_%', $id)) {
            return "article";
        }
        if (like_match('V%_%', $id)) {
            return "video";
        }
        if (like_match('P%_%', $id)) {
            return "podcast";
        }
        if (isMikrowav($id)) {
            return "mikrowav";
        }
        return "article"; //default to article
    }

    private function getDisabledCommentsData($ids)
    {
        return DB::table('disabled_comments')
            ->whereNull('deleted_at')
            ->whereIn('unique_ID', $ids)
            ->get()
            ->keyBy('unique_ID');
    }

    /**
     * ! deprecated. don't use it anymore
     */
    private function getCommentCounts($ids, $type = "article")
    {
        switch ($type) {
            case 'article':
                $table = "article_stats";
                break;
            case 'video':
                $table = "video_stats";
                break;
            case 'podcast':
                $table = "podcast_stats";
            default:
                break;
        }

        return DB::table($table)->select("{$type}_id as uniqueId", "comments")->whereIn("{$type}_id", $ids)->get()->keyBy("uniqueId");
    }

    public function getDisabledChannelsFromCache()
    {
        try {
            $value = Cache::remember("disabledChannels", now()->addMinutes(5), function () {
                return $this->getDisabledChannels();
            });
            return $value;
        } catch (\Exception $e) {
            notify_now($e);
            return $this->getDisabledChannels();
        }
    }

    public function getVideoByContentIdQuery(string $contentId) {
        return DB::table('videos')
            ->leftJoin('disabled_contents', 'videos.unique_id','=','disabled_contents.unique_id')
            ->where('videos.unique_id',$contentId);
    }

    public function getArticleByContentIdQuery(string $contentId) {
        return DB::table('article')
            ->leftJoin('disabled_contents', 'article.uniqueID','=','disabled_contents.unique_id')
            ->where('article.uniqueID', $contentId);
    }

    public function getPodcastByContentIdQuery(string $contentId) {
        return DB::table('podcasts')
            ->leftJoin('disabled_contents', 'podcasts.unique_id','=','disabled_contents.unique_id')
            ->where('podcasts.unique_id', $contentId);
    }

    public function getMikrowavByContentIdQuery(string $contentId) {
        return Mikrowav::query()
            ->with('media')
            ->select([
                'mikrowavs.*',
                'user_profile.username',
                'user_profile.profile_picture as user_profile_picture',
            ])
            ->leftJoin('disabled_contents', 'mikrowavs.unique_id','=','disabled_contents.unique_id')
            ->leftJoin('userLogin', 'userLogin.id','=','mikrowavs.user_id')
            ->leftJoin('user_profile', 'user_profile.user_id','=','userLogin.id')
            ->where('mikrowavs.unique_id', $contentId);
    }

    public function getGroupSlugByUniqueIds(array $uniqueIds): Collection{
        return DB::table('similar_contents')->whereIn('unique_id', $uniqueIds)->select('slug', 'unique_id')->get();
    }

    private function getDisabledChannels()
    {
        $results = DB::select('SELECT listing_enabled, GROUP_CONCAT(id) as d
        FROM channels
        where listing_enabled = ?
        GROUP by listing_enabled', [0]);
        if (count($results) != 1) return false;
        return $results[0]->d;
    }

    private function cleanScriptTag($content){
        //clean exact script tag that is used on android only
        //do this because of this error https://app.bugsnag.com/newswav/newswav-website/errors/61ea44a9cfa4ac000737025b?filters[error.status]=open&filters[event.since]=30d&filters[app.release_stage]=production&pivot_tab=event
        $script = 'Android.showImage(position);'; //lol
       return str_replace($script, "", $content);
    }

    private function excludeNotInterestedQuery($query, $columnName, $firebaseId){
        return $query->whereNotIn($columnName, function ($query) use ($firebaseId) {
            //don't let the haters consume our resources;
            $query->select('unique_id')->from('user_not_interested')->where('firebase_id', $firebaseId)->where('updated_at', '>=', Carbon::now()->subDays(30));
        });
    }
}
